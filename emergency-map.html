
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急一张图 - 广西交通运输应急管理系统</title>

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>

    <!-- 自定义样式 -->
    <style>
        /* 全局表格样式 - 内容居中对齐 */
        #emergency-event-modal table th {
            text-align: center !important;
            font-size: 16px !important;
            padding: 12px !important;
        }

        #emergency-event-modal table td {
            text-align: center !important;
            font-size: 15px !important;
            padding: 10px !important;
        }

        /* 增大字体大小 */
        #emergency-event-modal h4 {
            font-size: 24px !important;
        }

        #emergency-event-modal h5 {
            font-size: 20px !important;
        }

        #emergency-event-modal h6 {
            font-size: 18px !important;
        }

        /* 组织机构部分特别增大字体 */
        #emergency-event-modal .organization-structure h5 {
            font-size: 22px !important;
        }

        #emergency-event-modal .org-department h6 {
            font-size: 20px !important;
        }

        #emergency-event-modal .org-department p {
            font-size: 18px !important;
        }

        /* 组织机构标题间距调整 - 减少上方留白 */
        #emergency-event-modal .org-section h5 {
            font-size: 20px !important;
            margin-top: 5px !important;
            margin-bottom: 15px !important;
        }

        #emergency-event-modal .org-subsection h6 {
            font-size: 18px !important;
            margin-top: 0px !important;
            margin-bottom: 12px !important;
        }

        /* 组织机构内容字体增大 */
        #emergency-event-modal .org-subsection div {
            font-size: 16px !important;
            line-height: 1.6 !important;
            margin-bottom: 8px !important;
        }

        /* 组织机构容器间距调整 */
        #emergency-event-modal .org-subsection {
            margin-bottom: 15px !important;
            padding: 15px !important;
        }

        /* 物资列表和装备列表样式优化 */
        #emergency-event-modal .supply-item h6,
        #emergency-event-modal .rescue-team-item h6 {
            margin-top: 5px !important;
            margin-bottom: 8px !important;
        }

        /* 表格样式改进 - 保持颜色但优化视觉效果 */
        #emergency-event-modal table {
            border-radius: 6px !important;
            overflow: hidden !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
        }

        #emergency-event-modal table th {
            background: #2c3e50 !important;
            border: none !important;
            border-bottom: 2px solid #3498db !important;
            font-weight: bold !important;
            letter-spacing: 0.5px !important;
        }

        #emergency-event-modal table td {
            border: none !important;
            border-bottom: 1px solid #4a5f7a !important;
            transition: background-color 0.2s ease !important;
        }

        #emergency-event-modal table tbody tr:last-child td {
            border-bottom: none !important;
        }

        #emergency-event-modal table tbody tr:hover td {
            background-color: #3a4f66 !important;
        }

        /* 表格容器间距调整 */
        #emergency-event-modal .supply-item > div:last-child,
        #emergency-event-modal .rescue-team-item > div:last-child {
            margin-top: 8px !important;
        }

        /* 统一处理所有物资列表和装备列表的标题间距 */
        #emergency-event-modal .supply-item div[style*="margin-top: 10px"],
        #emergency-event-modal .rescue-team-item div[style*="margin-bottom: 10px"] {
            margin-top: 5px !important;
        }

        /* 统一处理所有物资列表和装备列表的标题样式 */
        #emergency-event-modal .supply-item h6,
        #emergency-event-modal .rescue-team-item h6 {
            margin-top: 5px !important;
            margin-bottom: 8px !important;
        }

        /* 各种卡片名称字体增大 */
        #emergency-event-modal .supply-name,
        #emergency-event-modal .team-name,
        #emergency-event-modal .vehicle-name,
        #emergency-event-modal .medical-name,
        #emergency-event-modal .fire-name,
        #emergency-event-modal .monitor-name {
            font-size: 20px !important;
        }

        /* 信息字段字体增大 */
        #emergency-event-modal .supply-info div,
        #emergency-event-modal .team-info div,
        #emergency-event-modal .vehicle-info div,
        #emergency-event-modal .medical-info div,
        #emergency-event-modal .fire-info div,
        #emergency-event-modal .monitor-info div {
            font-size: 16px !important;
        }

        #emergency-event-modal .supply-info span,
        #emergency-event-modal .team-info span,
        #emergency-event-modal .vehicle-info span,
        #emergency-event-modal .medical-info span,
        #emergency-event-modal .fire-info span,
        #emergency-event-modal .monitor-info span {
            font-size: 16px !important;
        }

        /* 基本信息字段增大 */
        #emergency-event-modal .info-item {
            font-size: 18px !important;
        }

        #emergency-event-modal .info-item strong {
            font-size: 20px !important;
        }

        #emergency-event-modal .info-item span {
            font-size: 18px !important;
        }

        /* 专家信息字体增大 */
        #emergency-event-modal .expert-name {
            font-size: 22px !important;
        }

        #emergency-event-modal .expert-info div {
            font-size: 18px !important;
        }

        #emergency-event-modal .expert-info span {
            font-size: 18px !important;
        }

        #emergency-event-modal .expert-specialty {
            font-size: 18px !important;
        }

        /* 事件预案启动与等级判别说明内容字体增大 */
        #emergency-event-modal .description-text {
            font-size: 18px !important;
            line-height: 1.8 !important;
        }

        #emergency-event-modal .plan-description {
            font-size: 18px !important;
            line-height: 1.8 !important;
        }

        /* 辅助决策内容字体增大 */
        #emergency-event-modal .decision-display {
            font-size: 18px !important;
            line-height: 1.8 !important;
        }

        #emergency-event-modal .decision-support textarea {
            font-size: 18px !important;
            line-height: 1.6 !important;
        }

        /* 确保所有段落文本都是统一大小 */
        #emergency-event-modal p {
            font-size: 18px !important;
            line-height: 1.8 !important;
        }

        /* 项目运营企业等其他文本内容 */
        #emergency-event-modal .info-panel p {
            font-size: 18px !important;
            line-height: 1.8 !important;
        }

        /* 项目运营企业联系信息字体增大 */
        #emergency-event-modal .enterprise-contact div {
            font-size: 16px !important;
        }

        #emergency-event-modal .enterprise-contact span {
            font-size: 16px !important;
        }

        /* 应急物资模态框样式 */
        #emergency-supply-modal table th {
            text-align: center !important;
            font-size: 16px !important;
            padding: 12px !important;
        }

        #emergency-supply-modal table td {
            text-align: center !important;
            font-size: 15px !important;
            padding: 10px !important;
        }

        #emergency-supply-modal h4 {
            font-size: 22px !important;
        }

        #emergency-supply-modal .info-item strong {
            font-size: 18px !important;
        }

        #emergency-supply-modal .info-item span {
            font-size: 16px !important;
        }

        /* 救援队伍模态框样式 */
        #rescue-team-modal table th {
            text-align: center !important;
            font-size: 16px !important;
            padding: 12px !important;
        }

        #rescue-team-modal table td {
            text-align: center !important;
            font-size: 15px !important;
            padding: 10px !important;
        }

        #rescue-team-modal h4 {
            font-size: 22px !important;
        }

        #rescue-team-modal .info-item strong {
            font-size: 18px !important;
        }

        #rescue-team-modal .info-item span {
            font-size: 16px !important;
        }

        /* 消防点模态框样式 */
        #fire-station-modal h4 {
            font-size: 22px !important;
        }

        #fire-station-modal .info-item strong {
            font-size: 18px !important;
        }

        #fire-station-modal .info-item span {
            font-size: 16px !important;
        }

        /* 医疗点模态框样式 */
        #medical-station-modal h4 {
            font-size: 22px !important;
        }

        #medical-station-modal .info-item strong {
            font-size: 18px !important;
        }

        #medical-station-modal .info-item span {
            font-size: 16px !important;
        }

        /* 救援车辆模态框样式 */
        #rescue-vehicle-modal h4 {
            font-size: 22px !important;
        }

        #rescue-vehicle-modal .info-item strong {
            font-size: 18px !important;
        }

        #rescue-vehicle-modal .info-item span {
            font-size: 16px !important;
        }

        /* 应急救援圈模态框样式 */
        #emergency-circle-modal .circle-map-container {
            border: 2px solid #95a5a6;
        }

        /* 应急救援圈模态框字体增大 */
        #emergency-circle-modal h3 {
            font-size: 24px !important;
        }

        #emergency-circle-modal h4 {
            font-size: 22px !important;
        }

        #emergency-circle-modal h5 {
            font-size: 20px !important;
        }

        #emergency-circle-modal h6 {
            font-size: 18px !important;
        }

        #emergency-circle-modal .modal-body {
            font-size: 16px !important;
        }

        /* 脉冲动画 */
        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        /* 救援圈按钮样式 */
        .circle-btn {
            background: #e67e22 !important;
            color: white !important;
            border: none !important;
            padding: 6px 12px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 12px !important;
            transition: background-color 0.3s ease !important;
        }

        .circle-btn:hover {
            background: #d35400 !important;
        }

        .circle-btn i {
            margin-right: 5px !important;
        }

        /* 救援圈点位信息卡片样式 */
        .circle-marker-tooltip {
            position: absolute;
            background: rgba(44, 62, 80, 0.95) !important;
            color: #ecf0f1 !important;
            padding: 12px 15px !important;
            border-radius: 8px !important;
            border: 1px solid #95a5a6 !important;
            font-size: 12px !important;
            line-height: 1.4 !important;
            min-width: 200px !important;
            max-width: 280px !important;
            z-index: 10001 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            pointer-events: none !important;
            opacity: 0 !important;
            transform: translateY(10px) !important;
            transition: all 0.3s ease !important;
        }

        .circle-marker-tooltip.show {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        .circle-marker-tooltip .tooltip-title {
            color: #3498db !important;
            font-weight: bold !important;
            font-size: 14px !important;
            margin-bottom: 8px !important;
            border-bottom: 1px solid #95a5a6 !important;
            padding-bottom: 5px !important;
        }

        .circle-marker-tooltip .tooltip-info {
            display: flex !important;
            flex-direction: column !important;
            gap: 4px !important;
        }

        .circle-marker-tooltip .tooltip-item {
            display: flex !important;
            justify-content: space-between !important;
        }

        .circle-marker-tooltip .tooltip-label {
            color: #95a5a6 !important;
            margin-right: 10px !important;
        }

        .circle-marker-tooltip .tooltip-value {
            color: #ecf0f1 !important;
            font-weight: 500 !important;
        }

        /* 救援圈动画效果 */
        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        /* 救援圈标点悬停效果增强 */
        .circle-resource-marker:hover {
            transform: scale(1.5) !important;
            z-index: 1003 !important;
            box-shadow: 0 4px 16px rgba(52, 152, 219, 0.8) !important;
        }

        /* 提示框样式增强 */
        .circle-marker-tooltip .tooltip-title {
            color: #3498db !important;
            font-weight: bold !important;
            font-size: 14px !important;
            margin-bottom: 8px !important;
            border-bottom: 1px solid #95a5a6 !important;
            padding-bottom: 5px !important;
        }

        .circle-marker-tooltip .tooltip-item {
            margin-bottom: 4px !important;
            display: flex !important;
            justify-content: space-between !important;
        }

        .circle-marker-tooltip .tooltip-label {
            color: #95a5a6 !important;
            font-size: 12px !important;
            min-width: 60px !important;
        }

        .circle-marker-tooltip .tooltip-value {
            color: #ecf0f1 !important;
            font-size: 12px !important;
            font-weight: 500 !important;
        }

        /* 资源列表样式 */
        .circle-resource-panel {
            scrollbar-width: thin;
            scrollbar-color: #95a5a6 #2c3e50;
        }

        .circle-resource-panel::-webkit-scrollbar {
            width: 8px;
        }

        .circle-resource-panel::-webkit-scrollbar-track {
            background: #2c3e50;
            border-radius: 4px;
        }

        .circle-resource-panel::-webkit-scrollbar-thumb {
            background: #95a5a6;
            border-radius: 4px;
        }

        .circle-resource-panel::-webkit-scrollbar-thumb:hover {
            background: #3498db;
        }

        .resource-list-item {
            position: relative;
            overflow: hidden;
        }

        .resource-list-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .resource-list-item:hover::before {
            left: 100%;
        }

        /* 搜索框样式 */
        #resource-search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        #resource-search-input::placeholder {
            color: #95a5a6;
        }

        /* 资源列表项动画 */
        .resource-list-item {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 地图标点聚焦动画增强 */
        @keyframes focusPulse {
            0%, 100% {
                transform: scale(2);
                opacity: 1;
            }
            50% {
                transform: scale(2.5);
                opacity: 0.8;
            }
        }

        /* 预案详情模态框子标签页样式 */
        .plan-sub-tabs {
            display: flex;
            border-bottom: 2px solid #34495e;
            margin-bottom: 20px;
            overflow-x: auto;
            gap: 0;
            padding: 0 20px;
        }

        .plan-sub-tab-btn {
            padding: 12px 20px;
            background: none;
            border: none;
            color: #95a5a6;
            border-bottom: 3px solid transparent;
            font-size: 16px;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.3s ease;
            min-width: fit-content;
        }

        .plan-sub-tab-btn:hover {
            color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .plan-sub-tab-btn.active {
            color: #3498db !important;
            border-bottom-color: #3498db !important;
            background: rgba(52, 152, 219, 0.1);
        }

        .plan-sub-tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .plan-sub-tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 预案内容区域样式 */
        .plan-content-section {
            background: #34495e;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .plan-content-title {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }

        .plan-content-text {
            color: #ecf0f1;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
        }

        .plan-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .plan-info-item {
            background: rgba(52, 73, 94, 0.5);
            padding: 15px;
            border-radius: 6px;
        }

        .plan-info-label {
            color: #95a5a6;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: bold;
        }

        .plan-info-value {
            color: #ecf0f1;
            font-size: 16px;
            margin: 0;
        }

        .plan-tag {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 5px;
            margin-bottom: 5px;
            display: inline-block;
        }

        /* 响应级别卡片样式 */
        .response-level-card {
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .response-level-header {
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .response-level-content {
            padding: 15px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .response-condition, .response-process {
            background: rgba(52, 73, 94, 0.5);
            padding: 15px;
            border-radius: 6px;
        }

        .response-subtitle {
            color: #95a5a6;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: bold;
        }

        .response-text {
            color: #ecf0f1;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }

        /* 组织体系卡片样式 */
        .org-card {
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .org-card-header {
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .org-card-content {
            padding: 15px;
        }

        .org-role-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .org-role-item {
            border: 1px solid #95a5a6;
            border-radius: 6px;
            padding: 12px;
        }

        .org-role-title {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .org-role-desc {
            color: #ecf0f1;
            font-size: 12px;
            margin: 0 0 8px 0;
            line-height: 1.4;
        }

        .org-contact {
            color: #95a5a6;
            font-size: 11px;
            margin: 0;
        }

        /* 预警级别样式 */
        .warning-level {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .warning-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 15px;
            min-width: 50px;
            text-align: center;
        }

        .warning-desc {
            color: #ecf0f1;
            font-size: 14px;
        }

        /* 保障类型网格样式 */
        .support-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .support-item {
            background: #34495e;
            padding: 20px;
            border-radius: 8px;
        }

        .support-title {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .support-content {
            background: rgba(52, 73, 94, 0.5);
            padding: 15px;
            border-radius: 6px;
        }

        .support-list {
            color: #ecf0f1;
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
            padding-left: 20px;
        }

        /* 操作按钮样式 */
        .plan-action-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .plan-action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .plan-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn-download {
            background: #3498db;
            color: white;
        }

        .btn-download:hover {
            background: #2980b9;
        }

        .btn-print {
            background: #27ae60;
            color: white;
        }

        .btn-print:hover {
            background: #229954;
        }

        .btn-other {
            background: #95a5a6;
            color: white;
        }

        .btn-other:hover {
            background: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 应急一张图内容 -->
                <div id="emergency-map-content" class="tab-content" style="display: flex;">
                    <aside class="left-sidebar">
                        <div class="resource-filter-container">
                            <!-- 1. 资源类型选择器 -->
                            <div class="resource-type-selector">
                                <h4>资源类型</h4>
                                <div class="resource-type-item select-all-res-types">
                                    <input type="checkbox" id="res-type-all" name="resource-type-all" checked onclick="toggleAllMarkers()">
                                    <label for="res-type-all"><strong>全选/全不选</strong></label>
                </div>

                                <div class="resource-type-tabs" style="position: relative;">
                                    <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                                        <button class="resource-tab-button active" onclick="showEmergencyEvents()">应急事件</button>
                                        <button class="resource-tab-button" onclick="showEmergencySupplies()">应急物资</button>
                                        <button class="resource-tab-button" onclick="showRescueTeams()">救援队伍</button>
                                        <button class="resource-tab-button" onclick="showOtherResources()">其他</button>
                </div>
                                </div>
                            </div>

                            <!-- 2. 各类型资源筛选内容 -->
                            <div class="resource-content-container">
                                <!-- 应急事件筛选内容 -->
                                <div id="emergency-events-content" class="resource-tab-content active">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="event-level-select">事件等级：</label>
                                                <select id="event-level-select" class="filter-select">
                                                    <option value="all">所有等级</option>
                                                    <option value="extra-major">特别重大</option>
                                                    <option value="major">重大</option>
                                                    <option value="large">较大</option>
                                                    <option value="general">一般</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="event-type-select">事件类型：</label>
                                                <select id="event-type-select" class="filter-select">
                                                    <option value="all">所有类型</option>
                                                    <option value="traffic-accident">交通事故</option>
                                                    <option value="natural-disaster">自然灾害</option>
                                                    <option value="equipment-failure">设备故障</option>
                                                    <option value="sudden-event">突发事件</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="event-status-select">处置状态：</label>
                                                <select id="event-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="pending">待处置</option>
                                                    <option value="processing">处置中</option>
                                                    <option value="completed">已处置</option>
                                                    <option value="closed">已结案</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 应急物资筛选内容 -->
                                <div id="emergency-supplies-content" class="resource-tab-content">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="supply-type-select">物资类型：</label>
                                                <select id="supply-type-select" class="filter-select">
                                                    <option value="all">所有类型</option>
                                                    <option value="rescue-equipment">救援装备</option>
                                                    <option value="medical-supplies">医疗用品</option>
                                                    <option value="living-supplies">生活物资</option>
                                                    <option value="communication-equipment">通信设备</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="supply-status-select">储备状态：</label>
                                                <select id="supply-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="sufficient">充足</option>
                                                    <option value="normal">一般</option>
                                                    <option value="insufficient">不足</option>
                                                    <option value="shortage">紧缺</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="supply-level-select">物资等级：</label>
                                                <select id="supply-level-select" class="filter-select">
                                                    <option value="all">所有等级</option>
                                                    <option value="level-1">一级储备</option>
                                                    <option value="level-2">二级储备</option>
                                                    <option value="level-3">三级储备</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 救援队伍筛选内容 -->
                                <div id="rescue-teams-content" class="resource-tab-content">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="team-type-select">队伍类型：</label>
                                                <select id="team-type-select" class="filter-select">
                                                    <option value="all">所有类型</option>
                                                    <option value="professional-rescue">专业救援队</option>
                                                    <option value="fire-rescue">消防救援队</option>
                                                    <option value="traffic-rescue">交通救援队</option>
                                                    <option value="medical-rescue">医疗救援队</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="team-status-select">队伍状态：</label>
                                                <select id="team-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="standby">待命</option>
                                                    <option value="dispatched">出动中</option>
                                                    <option value="on-mission">执行任务</option>
                                                    <option value="resting">休整中</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="team-level-select">队伍等级：</label>
                                                <select id="team-level-select" class="filter-select">
                                                    <option value="all">所有等级</option>
                                                    <option value="national">国家级</option>
                                                    <option value="provincial">省级</option>
                                                    <option value="municipal">市级</option>
                                                    <option value="county">县级</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 其他资源筛选内容 -->
                                <div id="other-resources-content" class="resource-tab-content">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item other-type-list-container">
                                                <label>资源类型：</label>
                                                <div class="other-type-list">
                                                    <div class="other-type-item">
                                                        <input type="checkbox" id="other-type-rescue-vehicle" name="other-type" value="rescue-vehicle" checked>
                                                        <label for="other-type-rescue-vehicle">救援车辆</label>
                                                    </div>
                                                    <div class="other-type-item">
                                                        <input type="checkbox" id="other-type-medical-point" name="other-type" value="medical-point" checked>
                                                        <label for="other-type-medical-point">医疗点</label>
                                                    </div>
                                                    <div class="other-type-item">
                                                        <input type="checkbox" id="other-type-fire-point" name="other-type" value="fire-point" checked>
                                                        <label for="other-type-fire-point">消防点</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="vehicle-type-select">车辆类型：</label>
                                                <select id="vehicle-type-select" class="filter-select">
                                                    <option value="all">所有车辆</option>
                                                    <option value="ambulance">救护车</option>
                                                    <option value="fire-truck">消防车</option>
                                                    <option value="wrecker">清障车</option>
                                                    <option value="engineering">工程车</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="facility-status-select">设施状态：</label>
                                                <select id="facility-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="available">可用</option>
                                                    <option value="busy">使用中</option>
                                                    <option value="maintenance">维护中</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 3. 资源条件筛选 -->
                            <div class="resource-condition-filter">
                                <div class="filter-tabs">
                                    <button class="filter-tab-button active" data-tab="unit" onclick="switchFilterTab(this, 'unit')">按单位划分</button>
                                    <button class="filter-tab-button" data-tab="road" onclick="switchFilterTab(this, 'road')">按路段划分</button>
                                </div>

                                <!-- 3.1 按单位划分内容 -->
                                <div id="filter-by-unit-content" class="filter-tab-content active">
                                    <ul class="collapsible-tree">
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt" value="gxtyt"><label for="unit-gxtyt">广西交通运输厅</label>
                                            <ul>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-nn" value="gxtyt-nn"><label for="unit-gxtyt-nn">南宁市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lz" value="gxtyt-lz"><label for="unit-gxtyt-lz">柳州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gl" value="gxtyt-gl"><label for="unit-gxtyt-gl">桂林市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-wz" value="gxtyt-wz"><label for="unit-gxtyt-wz">梧州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bh" value="gxtyt-bh"><label for="unit-gxtyt-bh">北海市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-fcg" value="gxtyt-fcg"><label for="unit-gxtyt-fcg">防城港市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-qz" value="gxtyt-qz"><label for="unit-gxtyt-qz">钦州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gg" value="gxtyt-gg"><label for="unit-gxtyt-gg">贵港市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-yl" value="gxtyt-yl"><label for="unit-gxtyt-yl">玉林市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bs" value="gxtyt-bs"><label for="unit-gxtyt-bs">百色市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hz" value="gxtyt-hz"><label for="unit-gxtyt-hz">贺州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hc" value="gxtyt-hc"><label for="unit-gxtyt-hc">河池市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lb" value="gxtyt-lb"><label for="unit-gxtyt-lb">来宾市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-cz" value="gxtyt-cz"><label for="unit-gxtyt-cz">崇左市交通运输局</label></li>
                                            </ul>
                                        </li>
                                        <li>
                                            <input type="checkbox" id="unit-qy" value="qy"><label for="unit-qy">企业</label>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 3.2 按路段划分内容 -->
                                <div id="filter-by-road-content" class="filter-tab-content">
                                    <ul class="collapsible-tree">
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="road-gl" value="gl"><label for="road-gl">公路</label>
                                            <ul>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gs" value="gl-gs"><label for="road-gl-gs">高速公路</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-gs-g72" value="gl-gs-g72"><label for="road-gl-gs-g72">G72</label></li>
                                                        <li><input type="checkbox" id="road-gl-gs-g80" value="gl-gs-g80"><label for="road-gl-gs-g80">G80</label></li>
                                                    </ul>
                                                </li>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gsgd" value="gl-gsgd"><label for="road-gl-gsgd">国省干道</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-gsgd-s201" value="gl-gsgd-s201"><label for="road-gl-gsgd-s201">S201</label></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="road-sl" value="sl"><label for="road-sl">水路</label>
                                            <ul>
                                                <li><input type="checkbox" id="road-sl-xn" value="sl-xn"><label for="road-sl-xn">西江航道</label></li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 4. 告警信息列表 -->
                            <div class="alert-list-container">
                                <h4>告警信息 <i class="fas fa-bell" style="color: #dc3545; margin-left: 5px;"></i></h4>
                                <div class="alert-tabs" style="display: flex; margin-bottom: 15px; border-bottom: 1px solid #ddd;">
                                    <button class="alert-tab-button active" data-tab="emergency-events" onclick="switchAlertTab(this, 'emergency-events')" style="flex: 1; padding: 10px 15px; border: none; background: #f8f9fa; color: #333; cursor: pointer; border-bottom: 2px solid #ff6b35; font-weight: bold;">应急事件</button>
                                    <button class="alert-tab-button" data-tab="verification-overdue" onclick="switchAlertTab(this, 'verification-overdue')" style="flex: 1; padding: 10px 15px; border: none; background: #f8f9fa; color: #666; cursor: pointer; border-bottom: 2px solid transparent;">校验超时</button>
                                </div>

                                <!-- 4.1 应急事件内容 -->
                                <div id="alert-emergency-events-content" class="alert-tab-content active" style="display: block;">
                                    <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                                        <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ff6b35; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-20 09:15</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; margin-right: 8px;">特别重大</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">南宁市G72高速K1499+500处发生交通事故，需紧急处置</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-19 14:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ff6b35; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">重大</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">柳州市S201省道K45+200处山体滑坡，道路中断</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-19 08:45</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">特别重大</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">桂林市灵川县X001县道K8+300处桥梁被洪水冲毁</span>
                                            </div>
                                        </li>
                                        <li class="alert-item low" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #28a745; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-18 16:20</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">一般</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">梧州市G80高速K890+100处车辆故障，占用应急车道</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-18 10:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">较大</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">北海市合浦县Y002乡道K5+800处路面积水严重</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 4.2 校验超时内容 -->
                                <div id="alert-verification-overdue-content" class="alert-tab-content" style="display: none;">
                                    <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-05-10 (超时10天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #007bff; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">应急预案</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">贺州市八步区G72高速公路交通事故应急预案</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-05-08 (超时12天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">应急物资</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">河池市金城江区应急救援物资库存校验</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-05-05 (超时15天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #17a2b8; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">应急通讯录</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">来宾市兴宾区应急救援队伍联系方式更新</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-05-03 (超时17天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #007bff; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">应急预案</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">崇左市江州区X012县道水毁应急预案</span>
                                            </div>
                                        </li>
                                        <li class="alert-item overdue-high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                            <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-04-30 (超时20天)</div>
                                            <div class="alert-content">
                                                <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #28a745; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">应急物资</span>
                                                <span class="alert-text" style="font-size: 14px; color: #333;">南宁市邕宁区G80高速应急救援装备检查</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </aside>

                    <section class="map-display-area">
                        <!-- 最新应急事件告警提示框 -->
                        <div class="latest-alert-container" id="emergency-map-alert" style="position: absolute; top: 15px; left: 15px; z-index: 1000; width: 480px;">
                            <div class="latest-alert high" style="background: rgba(255, 107, 53, 0.95); color: white; padding: 12px 15px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); display: flex; align-items: center; gap: 10px; font-size: 13px;">
                                <div class="alert-icon" style="font-size: 16px; flex-shrink: 0;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content" style="flex: 1;">
                                    <div class="alert-message" style="line-height: 1.4;">
                                        <span class="alert-level" style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px; margin-right: 8px; font-size: 11px; font-weight: bold;">重大</span>
                                        <span class="alert-text" style="font-size: 13px;">G72高速K1500处发生多车连环相撞事故</span>
                                    </div>
                                </div>
                                <div class="alert-actions" style="display: flex; gap: 8px; flex-shrink: 0;">
                                    <button class="alert-more-btn" onclick="document.getElementById('alert-emergency-events-content').scrollIntoView({behavior: 'smooth'})" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: bold;">
                                        查看详情
                    </button>
                                    <button class="alert-close-btn" onclick="document.getElementById('emergency-map-alert').style.display='none'" style="background: none; border: none; color: white; cursor: pointer; font-size: 14px; padding: 3px;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 应急管理按钮 -->
                        <div class="management-button-container">
                            <a href="plan_list.html" class="management-button">
                                <i class="fas fa-ambulance"></i> 应急处置管理
                        </a>
                    </div>

                        <!-- 地图图片 -->
                        <img src="lib/map_new.png" alt="广西地图" id="map-image">

                        <!-- 图例 - 应急资源主题 -->
                        <div class="map-legend" style="position: absolute !important; right: 15px !important; bottom: 15px !important; left: auto !important; top: auto !important; background: rgba(255, 255, 255, 0.98) !important; padding: 10px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 160px; z-index: 1000; font-size: 10px; border: 1px solid rgba(0,0,0,0.1);">
                            <div class="legend-section" style="margin-bottom: 8px;">
                                <div class="legend-title" style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #ff6b35; padding-bottom: 2px;">应急事件等级</div>
                                <div class="legend-items">
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(220, 53, 69, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">特别重大</div>
                </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(255, 107, 53, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">重大</div>
            </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(255, 193, 7, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">较大</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; background-color: rgba(40, 167, 69, 0.9); border-radius: 2px; margin-right: 5px;"></div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">一般</div>
                                    </div>
                                </div>
                            </div>

                            <div class="legend-section" style="margin-bottom: 8px;">
                                <div class="legend-title" style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #007bff; padding-bottom: 2px;">应急资源类型</div>
                                <div class="legend-items">
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(40, 167, 69, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-box" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">应急物资</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(23, 162, 184, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-users" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">救援队伍</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(138, 43, 226, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-ambulance" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">救援车辆</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(220, 53, 69, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-hospital" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">医疗点</div>
                                    </div>
                                    <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                                        <div class="legend-icon" style="width: 12px; height: 12px; display: flex; align-items: center; justify-content: center; background-color: rgba(255, 87, 34, 0.9); border-radius: 2px; margin-right: 5px;">
                                            <i class="fas fa-fire-extinguisher" style="font-size: 7px; color: white;"></i>
                                        </div>
                                        <div class="legend-text" style="font-size: 10px; color: #333;">消防点</div>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <!-- 地图上的标注点 - 应急事件点 -->
                        <!-- 河池区域 -->
                        <div class="map-marker event-marker" style="top: 295px; left: 252px; background-color: rgba(40, 167, 69, 0.8);" data-id="event001" data-type="events" data-event-level="normal" data-event-type="natural" data-event-status="processing">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker event-marker" style="top: 238px; left: 239px; background-color: rgba(220, 53, 69, 0.8);" data-id="event002" data-type="events" data-event-level="special" data-event-type="other" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker event-marker" style="top: 254px; left: 301px; background-color: rgba(220, 53, 69, 0.8);" data-id="event003" data-type="events" data-event-level="special" data-event-type="flood" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker event-marker" style="top: 374px; left: 225px; background-color: rgba(40, 167, 69, 0.8);" data-id="event004" data-type="events" data-event-level="normal" data-event-type="natural" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 贵港区域 -->
                        <div class="map-marker event-marker" style="top: 391px; left: 408px; background-color: rgba(40, 167, 69, 0.8);" data-id="event005" data-type="events" data-event-level="normal" data-event-type="collapse" data-event-status="processing">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 柳州区域 - 泉南高速山体塌方事故 -->
                        <div class="map-marker event-marker" style="top: 362px; left: 444px; background-color: rgba(255, 153, 0, 0.8);" data-id="event006" data-type="emergency-event" data-event-level="major" data-event-type="traffic-accident" data-event-status="processing" title="泉南高速山体塌方事故">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker event-marker" style="top: 315px; left: 395px; background-color: rgba(255, 193, 7, 0.8);" data-id="event007" data-type="events" data-event-level="large" data-event-type="flood" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker event-marker" style="top: 448px; left: 327px; background-color: rgba(255, 153, 0, 0.8);" data-id="event008" data-type="events" data-event-level="major" data-event-type="natural" data-event-status="completed">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker event-marker" style="top: 469px; left: 288px; background-color: rgba(255, 193, 7, 0.8);" data-id="event009" data-type="events" data-event-level="large" data-event-type="other" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker event-marker" style="top: 485px; left: 394px; background-color: rgba(40, 167, 69, 0.8);" data-id="event010" data-type="events" data-event-level="normal" data-event-type="traffic" data-event-status="processing">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker event-marker" style="top: 501px; left: 452px; background-color: rgba(220, 53, 69, 0.8);" data-id="event011" data-type="events" data-event-level="special" data-event-type="flood" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker event-marker" style="top: 466px; left: 494px; background-color: rgba(40, 167, 69, 0.8);" data-id="event012" data-type="events" data-event-level="normal" data-event-type="natural" data-event-status="completed">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker event-marker" style="top: 514px; left: 502px; background-color: rgba(255, 153, 0, 0.8);" data-id="event013" data-type="events" data-event-level="major" data-event-type="natural" data-event-status="completed">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 北海区域 -->
                        <div class="map-marker event-marker" style="top: 440px; left: 470px; background-color: rgba(220, 53, 69, 0.8);" data-id="event014" data-type="events" data-event-level="special" data-event-type="flood" data-event-status="processing">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker event-marker" style="top: 273px; left: 468px; background-color: rgba(220, 53, 69, 0.8);" data-id="event016" data-type="events" data-event-level="special" data-event-type="flood" data-event-status="pending">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker event-marker" style="top: 206px; left: 419px; background-color: rgba(220, 53, 69, 0.8);" data-id="event017" data-type="events" data-event-level="special" data-event-type="flood" data-event-status="processing">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker event-marker" style="top: 255px; left: 387px; background-color: rgba(255, 153, 0, 0.8);" data-id="event018" data-type="events" data-event-level="major" data-event-type="natural" data-event-status="completed">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>

                        <!-- 应急物资储备点 -->
                        <!-- 玉林区域 -->
                        <div class="map-marker supply-marker" style="top: 223px; left: 245px; display: none;" data-id="supply001" data-type="supplies" data-supply-type="rescue-equipment" data-status="sufficient">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker supply-marker" style="top: 243px; left: 297px; display: none;" data-id="supply002" data-type="supplies" data-supply-type="medical-supplies" data-status="normal">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker supply-marker" style="top: 326px; left: 248px; display: none;" data-id="supply003" data-type="supplies" data-supply-type="living-supplies" data-status="insufficient">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker supply-marker" style="top: 387px; left: 261px; display: none;" data-id="supply004" data-type="supplies" data-supply-type="rescue-equipment" data-status="insufficient">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 北海区域 -->
                        <div class="map-marker supply-marker" style="top: 475px; left: 274px; display: none;" data-id="supply005" data-type="supplies" data-supply-type="communication-equipment" data-status="insufficient">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker supply-marker" style="top: 428px; left: 371px; display: none;" data-id="supply006" data-type="supplies" data-supply-type="living-supplies" data-status="normal">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker supply-marker" style="top: 470px; left: 382px; display: none;" data-id="supply007" data-type="supplies" data-supply-type="living-supplies" data-status="insufficient">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 贵港区域 -->
                        <div class="map-marker supply-marker" style="top: 482px; left: 458px; display: none;" data-id="supply008" data-type="supplies" data-supply-type="communication-equipment" data-status="sufficient">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 北海区域 -->
                        <div class="map-marker supply-marker" style="top: 463px; left: 529px; display: none;" data-id="supply009" data-type="supplies" data-supply-type="rescue-equipment" data-status="shortage">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker supply-marker" style="top: 465px; left: 599px; display: none;" data-id="supply010" data-type="supplies" data-supply-type="rescue-equipment" data-status="shortage">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker supply-marker" style="top: 392px; left: 659px; display: none;" data-id="supply011" data-type="supplies" data-supply-type="communication-equipment" data-status="shortage">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker supply-marker" style="top: 371px; left: 583px; display: none;" data-id="supply012" data-type="supplies" data-supply-type="medical-supplies" data-status="shortage">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker supply-marker" style="top: 399px; left: 451px; display: none;" data-id="supply013" data-type="supplies" data-supply-type="communication-equipment" data-status="shortage">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker supply-marker" style="top: 328px; left: 378px; display: none;" data-id="supply014" data-type="supplies" data-supply-type="rescue-equipment" data-status="shortage">
                            <i class="fas fa-box"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker supply-marker" style="top: 311px; left: 350px; display: none;" data-id="supply015" data-type="supplies" data-supply-type="medical-supplies" data-status="insufficient">
                            <i class="fas fa-box"></i>
                        </div>

                        <!-- 救援队伍 -->
                        <!-- 百色区域 -->
                        <div class="map-marker team-marker" style="top: 238px; left: 202px; display: none;" data-id="team001" data-type="teams" data-team-type="fire-rescue" data-status="resting">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker team-marker" style="top: 264px; left: 230px; display: none;" data-id="team002" data-type="teams" data-team-type="medical-rescue" data-status="dispatched">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker team-marker" style="top: 366px; left: 204px; display: none;" data-id="team003" data-type="teams" data-team-type="traffic-rescue" data-status="standby">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker team-marker" style="top: 339px; left: 248px; display: none;" data-id="team004" data-type="teams" data-team-type="medical-rescue" data-status="standby">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker team-marker" style="top: 418px; left: 299px; display: none;" data-id="team005" data-type="teams" data-team-type="fire-rescue" data-status="standby">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker team-marker" style="top: 433px; left: 353px; display: none;" data-id="team006" data-type="teams" data-team-type="professional-rescue" data-status="dispatched">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 崇左区域 -->
                        <div class="map-marker team-marker" style="top: 390px; left: 382px; display: none;" data-id="team007" data-type="teams" data-team-type="professional-rescue" data-status="dispatched">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 贵港区域 -->
                        <div class="map-marker team-marker" style="top: 342px; left: 403px; display: none;" data-id="team008" data-type="teams" data-team-type="professional-rescue" data-status="standby">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker team-marker" style="top: 231px; left: 390px; display: none;" data-id="team009" data-type="teams" data-team-type="fire-rescue" data-status="resting">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker team-marker" style="top: 198px; left: 342px; display: none;" data-id="team010" data-type="teams" data-team-type="professional-rescue" data-status="resting">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 贺州区域 -->
                        <div class="map-marker team-marker" style="top: 181px; left: 302px; display: none;" data-id="team011" data-type="teams" data-team-type="traffic-rescue" data-status="standby">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker team-marker" style="top: 195px; left: 449px; display: none;" data-id="team012" data-type="teams" data-team-type="professional-rescue" data-status="dispatched">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 钦州区域 -->
                        <div class="map-marker team-marker" style="top: 149px; left: 514px; display: none;" data-id="team013" data-type="teams" data-team-type="professional-rescue" data-status="dispatched">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 玉林区域 -->
                        <div class="map-marker team-marker" style="top: 148px; left: 577px; display: none;" data-id="team014" data-type="teams" data-team-type="professional-rescue" data-status="standby">
                            <i class="fas fa-users"></i>
                        </div>
                        <!-- 北海区域 -->
                        <div class="map-marker team-marker" style="top: 95px; left: 582px; display: none;" data-id="team015" data-type="teams" data-team-type="medical-rescue" data-status="resting">
                            <i class="fas fa-users"></i>
                        </div>

                        <!-- 其他资源 - 救援车辆 -->
                        <!-- 来宾区域 -->
                        <div class="map-marker vehicle-marker" style="top: 208px; left: 171px; display: none;" data-id="vehicle001" data-type="vehicles" data-vehicle-type="fire-truck" data-status="available">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 贵港区域 -->
                        <div class="map-marker vehicle-marker" style="top: 238px; left: 139px; display: none;" data-id="vehicle002" data-type="vehicles" data-vehicle-type="engineering" data-status="dispatched">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker vehicle-marker" style="top: 350px; left: 183px; display: none;" data-id="vehicle003" data-type="vehicles" data-vehicle-type="engineering" data-status="dispatched">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker vehicle-marker" style="top: 215px; left: 321px; display: none;" data-id="vehicle004" data-type="vehicles" data-vehicle-type="ambulance" data-status="dispatched">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker vehicle-marker" style="top: 361px; left: 290px; display: none;" data-id="vehicle005" data-type="vehicles" data-vehicle-type="engineering" data-status="dispatched">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker vehicle-marker" style="top: 491px; left: 341px; display: none;" data-id="vehicle006" data-type="vehicles" data-vehicle-type="fire-truck" data-status="maintenance">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 来宾区域 -->
                        <div class="map-marker vehicle-marker" style="top: 392px; left: 341px; display: none;" data-id="vehicle007" data-type="vehicles" data-vehicle-type="transport" data-status="maintenance">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker vehicle-marker" style="top: 453px; left: 392px; display: none;" data-id="vehicle008" data-type="vehicles" data-vehicle-type="ambulance" data-status="maintenance">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 梧州区域 -->
                        <div class="map-marker vehicle-marker" style="top: 437px; left: 499px; display: none;" data-id="vehicle009" data-type="vehicles" data-vehicle-type="transport" data-status="maintenance">
                            <i class="fas fa-truck"></i>
                        </div>
                        <!-- 桂林区域 -->
                        <div class="map-marker vehicle-marker" style="top: 335px; left: 428px; display: none;" data-id="vehicle010" data-type="vehicles" data-vehicle-type="fire-truck" data-status="maintenance">
                            <i class="fas fa-truck"></i>
                        </div>

                        <!-- 其他资源 - 医疗点 -->
                        <!-- 贵港区域 -->
                        <div class="map-marker medical-marker" style="top: 208px; left: 247px; display: none;" data-id="medical001" data-type="medical" data-medical-type="hospital" data-status="available">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker medical-marker" style="top: 270px; left: 267px; display: none;" data-id="medical002" data-type="medical" data-medical-type="temp" data-status="unavailable">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker medical-marker" style="top: 405px; left: 289px; display: none;" data-id="medical003" data-type="medical" data-medical-type="mobile" data-status="available">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 防城港区域 -->
                        <div class="map-marker medical-marker" style="top: 372px; left: 367px; display: none;" data-id="medical004" data-type="medical" data-medical-type="hospital" data-status="available">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 贵港区域 -->
                        <div class="map-marker medical-marker" style="top: 483px; left: 521px; display: none;" data-id="medical006" data-type="medical" data-medical-type="hospital" data-status="available">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 河池区域 -->
                        <div class="map-marker medical-marker" style="top: 407px; left: 597px; display: none;" data-id="medical007" data-type="medical" data-medical-type="temp" data-status="available">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 南宁区域 -->
                        <div class="map-marker medical-marker" style="top: 442px; left: 600px; display: none;" data-id="medical008" data-type="medical" data-medical-type="temp" data-status="available">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 百色区域 -->
                        <div class="map-marker medical-marker" style="top: 346px; left: 671px; display: none;" data-id="medical009" data-type="medical" data-medical-type="mobile" data-status="unavailable">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <!-- 柳州区域 -->
                        <div class="map-marker medical-marker" style="top: 307px; left: 549px; display: none;" data-id="medical010" data-type="medical" data-medical-type="mobile" data-status="unavailable">
                            <i class="fas fa-hospital"></i>
                        </div>

                        <!-- 其他资源 - 消防点 -->
                        <!-- 南宁消防站 -->
                        <div class="map-marker fire-marker" style="top: 150px; left: 400px; display: none;" data-id="fire001" data-type="fire" data-fire-type="station" data-status="available">
                            <i class="fas fa-fire-extinguisher"></i>
                        </div>
                        <!-- 柳州消防站 -->
                        <div class="map-marker fire-marker" style="top: 125px; left: 550px; display: none;" data-id="fire002" data-type="fire" data-fire-type="equipment" data-status="available">
                            <i class="fas fa-fire-extinguisher"></i>
                        </div>
                        <!-- 桂林消防站 -->
                        <div class="map-marker fire-marker" style="top: 200px; left: 320px; display: none;" data-id="fire003" data-type="fire" data-fire-type="station" data-status="available">
                            <i class="fas fa-fire-extinguisher"></i>
                        </div>
                        <!-- 梧州消防站 -->
                        <div class="map-marker fire-marker" style="top: 180px; left: 480px; display: none;" data-id="fire004" data-type="fire" data-fire-type="equipment" data-status="maintenance">
                            <i class="fas fa-fire-extinguisher"></i>
                        </div>
                    </section>

                    <aside class="right-sidebar">
                        <div class="statistics-panel">
                            <h3>统计分析</h3>
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <div class="stat-label">应急事件数量</div>
                                    <div class="stat-value" id="event-count">28</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">救援队伍数量</div>
                                    <div class="stat-value" id="team-count">45</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">物资储备点</div>
                                    <div class="stat-value" id="supply-count">32</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">校验超时</div>
                                    <div class="stat-value risk-high-count" id="overdue-count">15</div>
                                </div>
                            </div>
                        </div>
                        <div class="risk-details-list">
                            <h3>详情列表</h3>

                                                    <!-- 详情列表标签卡 -->
                        <div class="details-tabs">
                            <button class="details-tab-button active" onclick="showEventDetails()">应急事件</button>
                            <button class="details-tab-button" onclick="showTeamDetails()">救援队伍</button>
                            <button class="details-tab-button" onclick="showSupplyDetails()">物资储备</button>
                            <button class="details-tab-button" onclick="showExpertDetails()">专家库</button>
                        </div>

                            <!-- 应急事件详情内容 -->
                            <div id="details-events-content" class="details-tab-content" style="display: block;">
                                <table class="details-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>地市</th>
                                            <th>事件名称</th>
                                            <th>事件等级</th>
                                            <th>发生时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>南宁市</td>
                                            <td>G72高速多车相撞</td>
                                            <td><span class="risk-level high">重大</span></td>
                                            <td>2024-05-20 09:15</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>桂林市</td>
                                            <td>漓江客船倾覆</td>
                                            <td><span class="risk-level high">特别重大</span></td>
                                            <td>2024-05-19 08:45</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>柳州市</td>
                                            <td>邕江大桥裂缝</td>
                                            <td><span class="risk-level medium">较大</span></td>
                                            <td>2024-05-19 14:30</td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>梧州市</td>
                                            <td>西江大桥震动</td>
                                            <td><span class="risk-level medium">较大</span></td>
                                            <td>2024-05-18 16:20</td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>钦州市</td>
                                            <td>危化品泄漏</td>
                                            <td><span class="risk-level medium">较大</span></td>
                                            <td>2024-05-18 10:30</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>防城港市</td>
                                            <td>东兴大桥桥面塌陷</td>
                                            <td><span class="risk-level high">重大</span></td>
                                            <td>2024-05-17 15:10</td>
                                        </tr>
                                        <tr>
                                            <td>7</td>
                                            <td>河池市</td>
                                            <td>G75高速山体滑坡</td>
                                            <td><span class="risk-level medium">较大</span></td>
                                            <td>2024-05-17 09:25</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>贵港市</td>
                                            <td>平南县山体滑坡</td>
                                            <td><span class="risk-level medium">重大</span></td>
                                            <td>2024-05-16 13:40</td>
                                        </tr>
                                        <tr>
                                            <td>9</td>
                                            <td>玉林市</td>
                                            <td>G72高速危化品泄漏</td>
                                            <td><span class="risk-level medium">较大</span></td>
                                            <td>2024-05-16 08:05</td>
                                        </tr>
                                        <tr>
                                            <td>10</td>
                                            <td>百色市</td>
                                            <td>S308省道大型塌方</td>
                                            <td><span class="risk-level high">特别重大</span></td>
                                            <td>2024-05-15 11:15</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 救援队伍详情内容 -->
                            <div id="details-teams-content" class="details-tab-content" style="display: none;">
                                <table class="details-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>地市</th>
                                            <th>队伍名称</th>
                                            <th>队伍类型</th>
                                            <th>人数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>南宁市</td>
                                            <td>南宁交通应急救援队</td>
                                            <td>专业救援</td>
                                            <td>45人</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>柳州市</td>
                                            <td>柳州消防救援支队</td>
                                            <td>消防救援</td>
                                            <td>68人</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>桂林市</td>
                                            <td>桂林水上救援队</td>
                                            <td>专业救援</td>
                                            <td>32人</td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>梧州市</td>
                                            <td>梧州交通救援队</td>
                                            <td>交通救援</td>
                                            <td>28人</td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>钦州市</td>
                                            <td>钦州港应急救援队</td>
                                            <td>专业救援</td>
                                            <td>52人</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>防城港市</td>
                                            <td>防城港海事救援队</td>
                                            <td>海事救援</td>
                                            <td>36人</td>
                                        </tr>
                                        <tr>
                                            <td>7</td>
                                            <td>贵港市</td>
                                            <td>贵港市应急救援队</td>
                                            <td>综合救援</td>
                                            <td>41人</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>玉林市</td>
                                            <td>玉林市消防救援支队</td>
                                            <td>消防救援</td>
                                            <td>59人</td>
                                        </tr>
                                        <tr>
                                            <td>9</td>
                                            <td>百色市</td>
                                            <td>百色山地救援队</td>
                                            <td>山地救援</td>
                                            <td>24人</td>
                                        </tr>
                                        <tr>
                                            <td>10</td>
                                            <td>贺州市</td>
                                            <td>贺州市交通救援队</td>
                                            <td>交通救援</td>
                                            <td>33人</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 物资储备详情内容 -->
                            <div id="details-supplies-content" class="details-tab-content" style="display: none;">
                                <table class="details-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>地市</th>
                                            <th>物资名称</th>
                                            <th>物资类型</th>
                                            <th>数量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>南宁市</td>
                                            <td>救生衣</td>
                                            <td>救援装备</td>
                                            <td>500件</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>柳州市</td>
                                            <td>医疗急救箱</td>
                                            <td>医疗用品</td>
                                            <td>200套</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>桂林市</td>
                                            <td>发电机</td>
                                            <td>设备物资</td>
                                            <td>50台</td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>梧州市</td>
                                            <td>应急通信设备</td>
                                            <td>通信设备</td>
                                            <td>80套</td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>钦州市</td>
                                            <td>消防设备</td>
                                            <td>消防物资</td>
                                            <td>100套</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>防城港市</td>
                                            <td>抽水泵</td>
                                            <td>防汛物资</td>
                                            <td>60台</td>
                                        </tr>
                                        <tr>
                                            <td>7</td>
                                            <td>贵港市</td>
                                            <td>应急照明灯</td>
                                            <td>设备物资</td>
                                            <td>150个</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>玉林市</td>
                                            <td>帐篷</td>
                                            <td>生活物资</td>
                                            <td>300顶</td>
                                        </tr>
                                        <tr>
                                            <td>9</td>
                                            <td>百色市</td>
                                            <td>便携式净水器</td>
                                            <td>生活物资</td>
                                            <td>40台</td>
                                        </tr>
                                        <tr>
                                            <td>10</td>
                                            <td>贺州市</td>
                                            <td>应急食品</td>
                                            <td>生活物资</td>
                                            <td>1000份</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 专家库详情内容 -->
                            <div id="details-experts-content" class="details-tab-content" style="display: none;">
                                <table class="details-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>地市</th>
                                            <th>姓名</th>
                                            <th>专业领域</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>南宁市</td>
                                            <td>李明</td>
                                            <td>桥梁工程</td>
                                            <td>广西交通设计院</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>柳州市</td>
                                            <td>王建华</td>
                                            <td>道路工程</td>
                                            <td>柳州市交通局</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>桂林市</td>
                                            <td>张伟</td>
                                            <td>地质灾害</td>
                                            <td>桂林理工大学</td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>梧州市</td>
                                            <td>陈丽</td>
                                            <td>应急管理</td>
                                            <td>梧州市应急局</td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>钦州市</td>
                                            <td>刘强</td>
                                            <td>港口工程</td>
                                            <td>钦州港集团</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>防城港市</td>
                                            <td>黄敏</td>
                                            <td>海事救援</td>
                                            <td>防城港海事局</td>
                                        </tr>
                                        <tr>
                                            <td>7</td>
                                            <td>贵港市</td>
                                            <td>周涛</td>
                                            <td>水运工程</td>
                                            <td>贵港市港务局</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>玉林市</td>
                                            <td>林芳</td>
                                            <td>交通安全</td>
                                            <td>玉林市交警支队</td>
                                        </tr>
                                        <tr>
                                            <td>9</td>
                                            <td>百色市</td>
                                            <td>马军</td>
                                            <td>山区道路</td>
                                            <td>百色市公路局</td>
                                        </tr>
                                        <tr>
                                            <td>10</td>
                                            <td>贺州市</td>
                                            <td>吴静</td>
                                            <td>隧道工程</td>
                                            <td>贺州市建设局</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </main>
    </div>

    <!-- 应急事件详情模态框 -->
    <div id="emergency-event-modal" class="modal" style="display: none; z-index: 9999 !important;">
        <div class="modal-content" style="max-width: 1200px; width: 95%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">应急事件详情</h3>
                <span class="close" onclick="closeEmergencyEventModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 1. 事件信息板块 -->
                <div class="event-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px; position: relative;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 18px;">事件信息</h4>
                        <button class="export-btn" onclick="exportEventInfo()" style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>

                    <!-- 基本信息 -->
                    <div class="basic-info-section" style="margin-bottom: 20px;">
                        <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">基本信息</h5>
                        <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">发生时间：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">2023年4月20日上午10:03</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">地点：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">泉南高速公路广西桂林至柳州段</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">道路编号：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">G72泉南高速</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">起止桩号：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">K1450+500-K1451+200</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">人员伤亡情况：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">10人受伤被困车内</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">事故原因：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">连续强降雨导致山体塌方</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">事故类型：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">交通事故+危化品泄漏</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label" style="color: #95a5a6; font-size: 12px;">预计恢复时间：</span>
                                <span class="info-value" style="color: #ecf0f1; font-size: 14px;">48小时</span>
                            </div>
                        </div>
                        <div class="info-item" style="margin-top: 15px;">
                            <span class="info-label" style="color: #95a5a6; font-size: 12px; display: block; margin-bottom: 5px;">影响范围及事态发展趋势：</span>
                            <span class="info-value" style="color: #ecf0f1; font-size: 14px;">桂林至柳州方向高速公路交通中断，槽罐车粗苯泄漏存在环境污染风险</span>
                        </div>
                        <div class="info-item" style="margin-top: 15px;">
                            <span class="info-label" style="color: #95a5a6; font-size: 12px; display: block; margin-bottom: 5px;">事件描述：</span>
                            <span class="info-value" style="color: #ecf0f1; font-size: 14px; line-height: 1.5;">因多日连续强降雨，导致泉南高速公路广西桂林至柳州段改扩建工程吴家屯隧道出口（柳州端）洞口上方山体塌方，致使一辆（装载33t粗苯）槽罐车撞上塌方体，随后因避让不及，导致一辆4.5t厢式货车、一辆载客30人客车、一辆小轿车连环追尾的交通事故，致使桂林至柳州方向高速公路交通中断，造成10人（轿车和客车各4人，厢式货车2人）受伤被困车内，同时槽罐车阀门受损、运输的粗苯泄漏，事故车辆不同程度损坏，部分道路设施损毁。</span>
                        </div>
                    </div>

                    <!-- 事件预案启动与等级判别说明 -->
                    <div class="plan-activation-section" style="margin-bottom: 20px;">
                        <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">事件预案启动与等级判别说明</h5>
                        <div class="plan-description" style="color: #ecf0f1; font-size: 18px; line-height: 1.8;">
                            根据《广西壮族自治区公路交通突发事件应急预案》，该预案适用于自治区范围内发生的Ⅱ级及以上公路交通突发事件。当国道、省道、高速公路发生交通中断，且抢修时间预计超过24小时时，应启动Ⅱ级应急响应。本次事件涉及泉南高速柳州段因山体塌方造成交通中断，伴随槽罐车粗苯泄漏和多车连环事故，抢险难度大、处置时间长，符合Ⅱ级响应启动条件。
                        </div>
                    </div>

                    <!-- 辅助决策 -->
                    <div class="decision-support-section" style="margin-bottom: 20px;">
                        <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">辅助决策</h5>
                        <div class="decision-display" style="color: #ecf0f1; font-size: 18px; line-height: 1.8; margin-bottom: 10px;">
                            该事故发生在由广西高速公路管理有限公司柳州分公司负责的高速公路路段，已判定为重大公路交通突发事件。根据《广西壮族自治区公路交通突发事件应急预案》，符合Ⅱ级响应启动条件，建议启动Ⅱ级应急响应，由自治区交通运输厅统一指挥和调度。推荐派遣危化品处置专家、隧道工程专家及应急救援专家共同参与，确保高效处置。<br>
                            根据事故现场山体塌方、危化品粗苯泄漏及多车人员被困等特点，需快速开展清障、救援和危化品转运处置工作。建议调配：挖掘机2台、装载机2台、运输车4辆；消防车1辆、救护车2辆、通讯保障车1辆；空载苯槽车1辆、单兵系统1套、无人机设备1套；具体配置可视现场情况动态调整。
                        </div>
                        <div class="decision-edit-section" style="display: none;">
                            <textarea id="decision-edit-text" style="width: 100%; height: 100px; background: #2c3e50; color: #ecf0f1; border: 1px solid #95a5a6; border-radius: 4px; padding: 10px; font-size: 18px; resize: vertical;"></textarea>
                            <div style="margin-top: 10px;">
                                <button onclick="confirmDecisionEdit()" style="background: #27ae60; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px;">确认</button>
                                <button onclick="cancelDecisionEdit()" style="background: #95a5a6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">取消</button>
                            </div>
                        </div>
                        <button onclick="editDecision()" style="background: #3498db; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            <i class="fas fa-edit"></i> 编辑决策
                        </button>
                    </div>

                    <!-- 项目运营企业 -->
                    <div class="enterprise-section">
                        <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">项目运营企业</h5>
                        <div class="enterprise-list" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                            <div class="enterprise-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                                <div class="enterprise-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px;">广西交通投资集团有限公司</div>
                                <div class="enterprise-contact">
                                    <div style="color: #95a5a6; font-size: 16px;">负责人：<span style="color: #ecf0f1; font-size: 16px;">张三</span></div>
                                    <div style="color: #95a5a6; font-size: 16px;">联系方式：<span style="color: #ecf0f1; font-size: 16px;">0771-5607119</span></div>
                                </div>
                            </div>
                            <div class="enterprise-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                                <div class="enterprise-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px;">广西高速公路管理有限公司柳州分公司</div>
                                <div class="enterprise-contact">
                                    <div style="color: #95a5a6; font-size: 16px;">负责人：<span style="color: #ecf0f1; font-size: 16px;">李四</span></div>
                                    <div style="color: #95a5a6; font-size: 16px;">联系方式：<span style="color: #ecf0f1; font-size: 16px;">0772-3825119</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. 推荐应急预案 -->
                <div class="emergency-plan-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">推荐应急预案</h4>
                    <div class="plan-info" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                        <div class="plan-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 16px;">广西壮族自治区公路交通突发事件应急预案</div>
                        <div class="plan-scope" style="color: #95a5a6; font-size: 12px; margin-bottom: 15px;">适用范围：<span style="color: #ecf0f1;">自治区范围内Ⅱ级及以上公路交通突发事件</span></div>
                        <div class="plan-actions">
                            <button onclick="viewPlanDetails()" style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px; font-size: 12px;">
                                <i class="fas fa-eye"></i> 查看预案详情
                            </button>
                            <button onclick="viewOtherPlans()" style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-list"></i> 查看其他预案
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 3. 推荐应急组织机构 -->
                <div class="organization-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">推荐应急组织机构</h4>

                    <!-- 自治区应急指挥机构 -->
                    <div class="org-section" style="margin-bottom: 20px;">
                        <h5 style="color: #95a5a6; margin-top: 5px; margin-bottom: 15px; font-size: 20px;">一、自治区应急指挥机构</h5>

                        <!-- 领导小组 -->
                        <div class="org-subsection" style="margin-bottom: 15px; background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">1、领导小组</h6>
                            <div style="margin-left: 15px;">
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">组长：李明华（自治区交通运输厅厅长）13907711001</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">副组长：张建国（自治区交通运输厅副厅长）13907712002</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">成员：王强（建设管理处处长）13907713001、刘华（安全监督处处长）13907713002、陈军（办公室主任）13907713003</div>
                            </div>
                        </div>

                        <!-- 领导小组办公室 -->
                        <div class="org-subsection" style="margin-bottom: 15px; background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">2、领导小组办公室</h6>
                            <div style="margin-left: 15px;">
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">主任：赵安全（厅安全总监）13907714001</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">副主任：王强（建设管理处负责人）13907713001</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">成员：李建设（建设管理处）13907714002、张安监（安全监督处）13907714003、周办公（办公室）13907714004</div>
                            </div>
                        </div>

                        <!-- 应急工作组 -->
                        <div class="org-subsection" style="margin-bottom: 15px; background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">3、应急工作组</h6>
                            <div style="margin-left: 15px;">
                                <div style="margin-bottom: 12px;">
                                    <div style="color: #95a5a6; font-size: 16px; font-weight: bold; line-height: 1.6; margin-bottom: 8px;">3.1 综合协调组</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">组长：陈军（厅办公室主任）13907713003</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">副组长：王强（建设管理处负责人）13907713001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">成员：李办公 13907715001、张建设 13907715002、周公路 13907715003</div>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <div style="color: #95a5a6; font-size: 16px; font-weight: bold; line-height: 1.6; margin-bottom: 8px;">3.2 应急指挥组</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">组长：王强（建设管理处负责人）13907713001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">副组长：刘公路（自治区公路发展中心负责人）13907716001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">成员：张建设 13907716002、李公路 13907716003、赵执法 13907716004</div>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <div style="color: #95a5a6; font-size: 16px; font-weight: bold; line-height: 1.6; margin-bottom: 8px;">3.3 运输保障组</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">组长：孙运输（综合运输管理处负责人）13907717001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">副组长：马道路（自治区道路运输发展中心负责人）13907717002</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">成员：钱运输 13907717003、吴道路 13907717004</div>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <div style="color: #95a5a6; font-size: 16px; font-weight: bold; line-height: 1.6; margin-bottom: 8px;">3.4 新闻宣传组</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">组长：郑党委（机关党委专职副书记）13907718001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">副组长：陈军（办公室负责人）13907713003</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">成员：冯办公 13907718002、何党委 13907718003、谢法规 13907718004</div>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <div style="color: #95a5a6; font-size: 16px; font-weight: bold; line-height: 1.6; margin-bottom: 8px;">3.5 通信保障组</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">组长：韩科教（科教处负责人）13907719001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">副组长：刘公路（自治区公路发展中心分管领导）13907716001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">成员：蒋科教 13907719002、彭公路 13907719003</div>
                                </div>
                                <div style="margin-bottom: 12px;">
                                    <div style="color: #95a5a6; font-size: 16px; font-weight: bold; line-height: 1.6; margin-bottom: 8px;">3.6 后勤保障组</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">组长：曾服务（机关服务中心负责人）13907720001</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">副组长：萧财务（财务处负责人）13907720002</div>
                                    <div style="color: #ecf0f1; font-size: 15px; margin-left: 10px; line-height: 1.6; margin-bottom: 5px;">成员：颜财务 13907720003、常公安 13907720004、邹服务 13907720005</div>
                                </div>
                            </div>
                        </div>

                        <!-- 现场工作组 -->
                        <div class="org-subsection" style="margin-bottom: 15px; background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">4、现场工作组</h6>
                            <div style="margin-left: 15px;">
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">成员：苗技术（专业技术人员）13907721001、范专家（技术专家）13907721002、汤现场（现场协调员）13907721003、雷安全（安全监督员）13907721004</div>
                            </div>
                        </div>

                        <!-- 专家组 -->
                        <div class="org-subsection" style="margin-bottom: 15px; background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">5、专家组</h6>
                            <div style="margin-left: 15px;">
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">专家：黎建华（隧道工程专家，广西大学教授）13807711001</div>
                            </div>
                        </div>
                    </div>

                    <!-- 市、县级应急指挥机构 -->
                    <div class="org-section">
                        <h5 style="color: #95a5a6; margin-top: 5px; margin-bottom: 15px; font-size: 20px;">二、市、县级应急指挥机构</h5>

                        <!-- 柳州市应急指挥机构 -->
                        <div class="org-subsection" style="margin-bottom: 15px; background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">柳州市应急指挥机构</h6>
                            <div style="margin-left: 15px;">
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">指挥长：梁市长（柳州市交通运输局局长）0772-2825001</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">副指挥长：温副局（柳州市交通运输局副局长）0772-2825002</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">成员：莫建设（建设科科长）0772-2825003、卢安全（安全科科长）0772-2825004、韦公路（柳州市公路中心主任）0772-2825005</div>
                            </div>
                        </div>

                        <!-- 柳江区应急指挥机构 -->
                        <div class="org-subsection" style="background: #2c3e50; padding: 15px; border-radius: 6px;">
                            <h6 style="color: #3498db; margin-top: 0px; margin-bottom: 12px; font-size: 18px;">柳江区应急指挥机构</h6>
                            <div style="margin-left: 15px;">
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">指挥长：覃主任（柳江区交通运输局局长）0772-7212001</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">副指挥长：农副局（柳江区交通运输局副局长）0772-7212002</div>
                                <div style="color: #ecf0f1; margin-bottom: 8px; font-size: 16px; line-height: 1.6;">成员：石建设（建设股股长）0772-7212003、龙安全（安全股股长）0772-7212004、班公路（柳江区公路中心主任）0772-7212005</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 4. 推荐专家 -->
                <div class="experts-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 18px;">推荐专家</h4>
                        <button class="circle-btn" onclick="openCircleModal('experts')">
                            <i class="fas fa-map-marked-alt"></i>应急救援圈
                        </button>
                    </div>
                    <div class="experts-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                        <div class="expert-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                            <div class="expert-name" style="color: #3498db; font-weight: bold; margin-bottom: 5px;">李建华 - 隧道工程专家</div>
                            <div class="expert-title" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">广西大学土木建筑工程学院教授</div>
                            <div class="expert-distance" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">距离事故点：约180公里</div>
                            <div class="expert-contact" style="color: #ecf0f1; font-size: 12px;">联系电话：13807711001</div>
                        </div>
                        <div class="expert-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                            <div class="expert-name" style="color: #3498db; font-weight: bold; margin-bottom: 5px;">张明 - 危化品处置专家</div>
                            <div class="expert-title" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">广西安全生产科学研究院高级工程师</div>
                            <div class="expert-distance" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">距离事故点：约175公里</div>
                            <div class="expert-contact" style="color: #ecf0f1; font-size: 12px;">联系电话：13907712002</div>
                        </div>
                        <div class="expert-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                            <div class="expert-name" style="color: #3498db; font-weight: bold; margin-bottom: 5px;">王强 - 应急救援专家</div>
                            <div class="expert-title" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">柳州市应急管理局副局长</div>
                            <div class="expert-distance" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">距离事故点：约15公里</div>
                            <div class="expert-contact" style="color: #ecf0f1; font-size: 12px;">联系电话：13977213003</div>
                        </div>
                    </div>
                </div>

                <!-- 6. 附近救援队伍 -->
                <div class="rescue-teams-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 18px;">附近救援队伍</h4>
                        <button class="circle-btn" onclick="openCircleModal('teams')">
                            <i class="fas fa-map-marked-alt"></i>应急救援圈
                        </button>
                    </div>

                    <!-- 20km范围内 -->
                    <div class="rescue-section" style="margin-bottom: 20px;">
                        <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 16px;">20km范围内</h5>
                        <div class="rescue-teams-list">
                            <div class="rescue-team-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                                <div class="team-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">柳州市消防救援支队</div>
                                <div class="team-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                                    <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">柳州市城中区</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">12km</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">张支队长</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-119</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">队伍人数：<span style="color: #ecf0f1;">45人</span></div>
                                </div>

                                <!-- 物资列表 -->
                                <div style="margin-bottom: 10px;">
                                    <h6 style="color: #95a5a6; margin-top: 5px; margin-bottom: 8px; font-size: 14px;">物资列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">物资名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">消防车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">8辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">救援器材</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">挖掘机</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">2台</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">装载机</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">2台</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 装备列表 -->
                                <div>
                                    <h6 style="color: #95a5a6; margin-top: 5px; margin-bottom: 8px; font-size: 14px;">装备列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">装备名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">破拆工具</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">20套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">防护服</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">50套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">单兵系统</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">无人机设备</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1套</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="rescue-team-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                                <div class="team-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">柳江区应急救援队</div>
                                <div class="team-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                                    <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">柳江区拉堡镇</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">8km</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">陈队长</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-7212001</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">队伍人数：<span style="color: #ecf0f1;">30人</span></div>
                                </div>

                                <!-- 物资列表 -->
                                <div style="margin-bottom: 10px;">
                                    <h6 style="color: #95a5a6; margin-top: 5px; margin-bottom: 8px; font-size: 14px;">物资列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">物资名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">消防车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">8辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">救援器材</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">挖掘机</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">2台</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">装载机</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">2台</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 装备列表 -->
                                <div>
                                    <h6 style="color: #95a5a6; margin-top: 5px; margin-bottom: 8px; font-size: 14px;">装备列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">装备名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">破拆工具</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">20套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">防护服</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">50套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">单兵系统</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">无人机设备</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1套</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 40km范围内 -->
                    <div class="rescue-section">
                        <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 16px;">40km范围内</h5>
                        <div class="rescue-teams-list">
                            <div class="rescue-team-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                                <div class="team-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">桂林市专业救援队</div>
                                <div class="team-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                                    <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">桂林市象山区</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">35km</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">李队长</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0773-2825001</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">队伍人数：<span style="color: #ecf0f1;">60人</span></div>
                                </div>

                                <!-- 物资列表 -->
                                <div style="margin-bottom: 10px;">
                                    <h6 style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">物资列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">物资名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">专业救援车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">12辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">空载苯槽车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">运输车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">4辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">通讯保障车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1辆</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 装备列表 -->
                                <div>
                                    <h6 style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">装备列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">装备名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">危化品处置设备</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">2套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">化学防护服</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">30套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">气体检测仪</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">10台</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">应急通信设备</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">5套</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="rescue-team-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                                <div class="team-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">广西危化品处置队</div>
                                <div class="team-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                                    <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">南宁市青秀区</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">38km</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">王队长</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0771-5607001</span></div>
                                    <div style="color: #95a5a6; font-size: 14px;">队伍人数：<span style="color: #ecf0f1;">25人</span></div>
                                </div>

                                <!-- 物资列表 -->
                                <div style="margin-bottom: 10px;">
                                    <h6 style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">物资列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">物资名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">专业救援车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">12辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">空载苯槽车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">运输车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">4辆</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">通讯保障车</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">1辆</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 装备列表 -->
                                <div>
                                    <h6 style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">装备列表：</h6>
                                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                                        <thead>
                                            <tr>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">装备名称</th>
                                                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px;">数量</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">危化品处置设备</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">2套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">化学防护服</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">30套</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">气体检测仪</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">10台</td>
                                            </tr>
                                            <tr>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">应急通信设备</td>
                                                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">5套</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 8. 医疗单位 -->
                <div class="medical-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 20px;">医疗单位</h4>
                        <button class="circle-btn" onclick="openCircleModal('medical')">
                            <i class="fas fa-map-marked-alt"></i>应急救援圈
                        </button>
                    </div>
                    <div class="medical-list">
                        <div class="medical-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="medical-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">柳州市人民医院</div>
                            <div class="medical-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                                <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">柳州市城中区文昌路8号</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">18km</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">张院长</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-2662222</span></div>
                            </div>
                        </div>

                        <div class="medical-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="medical-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">柳江区人民医院</div>
                            <div class="medical-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                                <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">柳江区拉堡镇建设中路</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">8km</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">李院长</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-7212120</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 9. 消防单位 -->
                <div class="fire-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 20px;">消防单位</h4>
                        <button class="circle-btn" onclick="openCircleModal('fire')">
                            <i class="fas fa-map-marked-alt"></i>应急救援圈
                        </button>
                    </div>
                    <div class="fire-list">
                        <div class="fire-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="fire-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">柳州市消防救援支队</div>
                            <div class="fire-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                                <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">柳州市城中区中山中路</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">12km</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">张支队长</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-119</span></div>
                            </div>
                        </div>

                        <div class="fire-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="fire-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">柳江区消防救援大队</div>
                            <div class="fire-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                                <div style="color: #95a5a6; font-size: 14px;">地点：<span style="color: #ecf0f1;">柳江区拉堡镇柳堡路</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">6km</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">王大队长</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-7212119</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 10. 事故附近监控视频 -->
                <div class="monitoring-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 20px;">事故附近监控视频</h4>
                    <div class="monitoring-list">
                        <div class="monitoring-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="monitoring-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">吴家屯隧道出口监控</div>
                            <div class="monitoring-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                                <div style="color: #95a5a6; font-size: 14px;">路段编号：<span style="color: #ecf0f1;">G72</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">起止桩号：<span style="color: #ecf0f1;">K1450+800</span></div>
                                <div>
                                    <button onclick="viewMonitoring('camera1')" style="background: #27ae60; color: white; border: none; padding: 10px 18px; border-radius: 4px; cursor: pointer; font-size: 14px;">查看监控</button>
                                </div>
                            </div>
                        </div>

                        <div class="monitoring-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="monitoring-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">事故路段监控</div>
                            <div class="monitoring-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                                <div style="color: #95a5a6; font-size: 14px;">路段编号：<span style="color: #ecf0f1;">G72</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">起止桩号：<span style="color: #ecf0f1;">K1451+000</span></div>
                                <div>
                                    <button onclick="viewMonitoring('camera2')" style="background: #27ae60; color: white; border: none; padding: 10px 18px; border-radius: 4px; cursor: pointer; font-size: 14px;">查看监控</button>
                                </div>
                            </div>
                        </div>

                        <div class="monitoring-item" style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
                            <div class="monitoring-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">隧道入口监控</div>
                            <div class="monitoring-info" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                                <div style="color: #95a5a6; font-size: 14px;">路段编号：<span style="color: #ecf0f1;">G72</span></div>
                                <div style="color: #95a5a6; font-size: 14px;">起止桩号：<span style="color: #ecf0f1;">K1449+500</span></div>
                                <div>
                                    <button onclick="viewMonitoring('camera3')" style="background: #27ae60; color: white; border: none; padding: 10px 18px; border-radius: 4px; cursor: pointer; font-size: 14px;">查看监控</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 应急物资详情模态框 -->
    <div id="emergency-supply-modal" class="modal" style="display: none; z-index: 9999 !important;">
        <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">应急物资详情</h3>
                <span class="close" onclick="closeSupplyModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 基本信息 -->
                <div class="supply-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
                    </div>

                    <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市应急物资储备中心</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">位置：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市城中区文昌路168号</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">张主任</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">0772-3825001</span>
                        </div>
                        <div class="info-item" style="grid-column: span 2;">
                            <strong style="color: #3498db; font-size: 18px;">最新更新时间：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">2023年4月20日 09:30</span>
                        </div>
                    </div>
                </div>

                <!-- 物资列表 -->
                <div class="materials-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">物资列表</h4>
                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                        <thead>
                            <tr>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">物资名称</th>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">型号</th>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">救生衣</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">标准型</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">200件</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">医疗箱</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">急救型</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">50套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">发电机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">5KW</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">10台</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">帐篷</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">4人型</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">30顶</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">照明设备</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">LED强光</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">25套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">通信设备</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">对讲机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">40台</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 救援队伍详情模态框 -->
    <div id="rescue-team-modal" class="modal" style="display: none; z-index: 9999 !important;">
        <div class="modal-content" style="max-width: 1000px; width: 95%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">救援队伍详情</h3>
                <span class="close" onclick="closeTeamModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 基本信息 -->
                <div class="team-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
                    </div>

                    <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市消防救援支队</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">位置：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市城中区中山中路</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">专业方向：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">消防救援、危化品处置</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">张支队长</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">0772-119</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">队伍人数：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">45人</span>
                        </div>
                        <div class="info-item" style="grid-column: span 2;">
                            <strong style="color: #3498db; font-size: 18px;">最新更新时间：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">2023年4月20日 08:45</span>
                        </div>
                    </div>
                </div>

                <!-- 物资列表 -->
                <div class="team-materials-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">物资列表</h4>
                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                        <thead>
                            <tr>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">物资名称</th>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">型号</th>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">消防车</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">泡沫消防车</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">8辆</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">救援器材</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">综合救援</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">1套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">挖掘机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">小型挖掘机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">2台</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">装载机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">轮式装载机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">2台</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 装备列表 -->
                <div class="team-equipment-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">装备列表</h4>
                    <table style="width: 100%; border-collapse: collapse; background: #34495e;">
                        <thead>
                            <tr>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">装备名称</th>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">型号</th>
                                <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">破拆工具</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">液压破拆器</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">20套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">防护服</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">化学防护服</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">50套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">单兵系统</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">数字化单兵</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">1套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">无人机设备</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">航拍无人机</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">1套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">呼吸器</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">正压式空气呼吸器</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">30套</td>
                            </tr>
                            <tr>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">生命探测仪</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">声波生命探测仪</td>
                                <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">2台</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 消防点详情模态框 -->
    <div id="fire-station-modal" class="modal" style="display: none; z-index: 9999 !important;">
        <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">消防点详情</h3>
                <span class="close" onclick="closeFireStationModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 基本信息 -->
                <div class="fire-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
                    <div style="margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
                    </div>

                    <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市消防救援支队</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">地点：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市城中区中山中路</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">张支队长</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">0772-119</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 医疗点详情模态框 -->
    <div id="medical-station-modal" class="modal" style="display: none; z-index: 9999 !important;">
        <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">医疗点详情</h3>
                <span class="close" onclick="closeMedicalStationModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 基本信息 -->
                <div class="medical-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
                    <div style="margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
                    </div>

                    <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市人民医院</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">地点：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">柳州市城中区文昌路8号</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">张院长</span>
                        </div>
                        <div class="info-item">
                            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
                            <span style="color: #ecf0f1; font-size: 16px;">0772-2662222</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 应急救援圈模态框 -->
    <div id="emergency-circle-modal" class="modal" style="display: none; z-index: 10000 !important;">
        <div class="modal-content" style="max-width: 1600px; width: 98%; max-height: 95vh; overflow: hidden;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 id="circle-modal-title" style="margin: 0; font-size: 20px;">应急救援圈</h3>
                <span class="close" onclick="closeCircleModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1; display: flex; gap: 20px; height: calc(95vh - 80px); overflow: hidden;">

                <!-- 左侧地图容器 -->
                <div class="circle-map-section" style="flex: 2; display: flex; flex-direction: column;">
                    <div class="circle-map-container" style="position: relative; width: 100%; height: 600px; background: #34495e; border-radius: 8px; overflow: hidden; border: 2px solid #95a5a6;">
                        <!-- 背景地图 - 放大显示以应急事件为中心的区域 -->
                        <img src="lib/map_new.png" class="circle-map-image" style="width: 200%; height: 200%; object-fit: cover; position: absolute; top: -50%; left: -50%; transform: translate(25%, 25%);">

                        <!-- 地图放大说明 -->
                        <div style="position: absolute; top: 10px; left: 10px; background: rgba(44, 62, 80, 0.9); color: #ecf0f1; padding: 8px 12px; border-radius: 4px; font-size: 16px; z-index: 1001;">
                            以应急事件为中心的局部区域
                        </div>

                        <!-- 应急事件中心点 -->
                        <div class="emergency-center-point" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 20px; height: 20px; background: #e74c3c; border: 3px solid #fff; border-radius: 50%; box-shadow: 0 0 20px rgba(231, 76, 60, 0.8); animation: pulse 2s infinite;">
                            <div style="position: absolute; top: -5px; left: -5px; width: 30px; height: 30px; border: 2px solid #e74c3c; border-radius: 50%; animation: pulse-ring 2s infinite;"></div>
                        </div>

                        <!-- 20km范围圆圈 -->
                        <div class="range-circle-20km" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; border: 4px dashed #ff6b35; border-radius: 50%; pointer-events: none; opacity: 0.9; box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);">
                            <div style="position: absolute; top: -30px; left: 50%; transform: translateX(-50%); background: #ff6b35; color: white; padding: 6px 15px; border-radius: 6px; font-size: 16px; font-weight: bold; box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);">20km</div>
                        </div>

                        <!-- 40km范围圆圈 -->
                        <div class="range-circle-40km" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 400px; height: 400px; border: 4px dashed #3498db; border-radius: 50%; pointer-events: none; opacity: 0.9; box-shadow: 0 0 15px rgba(52, 152, 219, 0.4);">
                            <div style="position: absolute; top: -30px; left: 50%; transform: translateX(-50%); background: #3498db; color: white; padding: 6px 15px; border-radius: 6px; font-size: 16px; font-weight: bold; box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);">40km</div>
                        </div>

                        <!-- 动态资源标点容器 -->
                        <div id="circle-markers-container"></div>
                    </div>

                    <!-- 统计信息和图例 -->
                    <div class="circle-info-panel" style="background: #34495e; padding: 15px; border-radius: 8px; margin-top: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="circle-statistics">
                                <h5 style="color: #3498db; margin: 0 0 10px 0; font-size: 20px;">资源统计</h5>
                                <div style="display: flex; gap: 30px;">
                                    <div style="color: #ecf0f1; font-size: 18px;">
                                        <span style="color: #3498db;">20km范围内：</span>
                                        <span id="count-20km">0</span> 个
                                    </div>
                                    <div style="color: #ecf0f1; font-size: 18px;">
                                        <span style="color: #27ae60;">40km范围内：</span>
                                        <span id="count-40km">0</span> 个
                                    </div>
                                    <div style="color: #ecf0f1; font-size: 18px;">
                                        <span style="color: #95a5a6;">总计：</span>
                                        <span id="count-total">0</span> 个
                                    </div>
                                </div>
                            </div>
                            <div class="circle-legend">
                                <h5 style="color: #3498db; margin: 0 0 10px 0; font-size: 20px;">图例</h5>
                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                    <div style="display: flex; align-items: center; gap: 8px; color: #ecf0f1; font-size: 16px;">
                                        <div style="width: 12px; height: 12px; background: #e74c3c; border-radius: 50%;"></div>
                                        <span>应急事件中心</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; color: #ecf0f1; font-size: 16px;">
                                        <div style="width: 20px; height: 2px; border-top: 2px dashed #3498db;"></div>
                                        <span>20km范围</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px; color: #ecf0f1; font-size: 16px;">
                                        <div style="width: 20px; height: 2px; border-top: 2px dashed #27ae60;"></div>
                                        <span>40km范围</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧资源列表面板 -->
                <div class="circle-resource-panel" style="flex: 1; background: #34495e; border-radius: 8px; padding: 20px; overflow-y: auto; border: 2px solid #95a5a6;">
                    <!-- 搜索栏 -->
                    <div class="resource-search" style="margin-bottom: 20px;">
                        <h4 id="resource-panel-title" style="color: #3498db; margin: 0 0 15px 0; font-size: 22px;">资源列表</h4>
                        <div style="position: relative;">
                            <input type="text" id="resource-search-input" placeholder="搜索资源..." style="width: 100%; padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
                        </div>
                    </div>

                    <!-- 资源列表容器 -->
                    <div id="resource-list-container" style="display: flex; flex-direction: column; gap: 12px;">
                        <!-- 资源项目将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预案详情模态框 -->
    <div id="plan-details-modal" class="modal" style="display: none; z-index: 10000 !important;">
        <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">预案详情</h3>
                <span class="close" onclick="closePlanDetailsModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">
                <div class="plan-details-content">
                    <div class="plan-sub-tabs">
                        <button class="plan-sub-tab-btn active" data-plan-tab="basic-info" style="color: #3498db; border-bottom: 2px solid #3498db;">基本信息 & 总则</button>
                        <button class="plan-sub-tab-btn" data-plan-tab="organization" style="color: #95a5a6; border-bottom: 2px solid transparent;">组织体系</button>
                        <button class="plan-sub-tab-btn" data-plan-tab="prevention" style="color: #95a5a6; border-bottom: 2px solid transparent;">预防与预警</button>
                        <button class="plan-sub-tab-btn" data-plan-tab="response" style="color: #95a5a6; border-bottom: 2px solid transparent;">应急响应</button>
                        <button class="plan-sub-tab-btn" data-plan-tab="post-processing" style="color: #95a5a6; border-bottom: 2px solid transparent;">后期处置</button>
                        <button class="plan-sub-tab-btn" data-plan-tab="emergency-support" style="color: #95a5a6; border-bottom: 2px solid transparent;">应急保障</button>
                        <button class="plan-sub-tab-btn" data-plan-tab="plan-management" style="color: #95a5a6; border-bottom: 2px solid transparent;">预案管理</button>
                    </div>
                    <div class="plan-sub-tab-content" id="basic-info" style="display: block;">
                        <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">基本信息 & 总则</h3>
                        
                        <!-- 基本信息 -->
                        <div style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">基本信息</h4>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                                <div>
                                    <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">预案名称</p>
                                    <p style="color: #ecf0f1; font-size: 16px; margin: 0;">广西壮族自治区公路交通突发事件应急预案</p>
                                </div>
                                <div>
                                    <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">预案类型</p>
                                    <p style="color: #ecf0f1; font-size: 16px; margin: 0;">公路交通类</p>
                                </div>
                                <div>
                                    <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">编制单位</p>
                                    <p style="color: #ecf0f1; font-size: 16px; margin: 0;">广西交通运输厅</p>
                                </div>
                                <div>
                                    <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">编制时间</p>
                                    <p style="color: #ecf0f1; font-size: 16px; margin: 0;">2023年11月15日</p>
                                </div>
                                <div style="grid-column: span 2;">
                                    <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">适用范围</p>
                                    <p style="color: #ecf0f1; font-size: 16px; margin: 0;">自治区行政区域内发生的Ⅱ级及以上公路交通突发事件的应对工作</p>
                                </div>
                            </div>
                        </div>

                        <!-- 编制目的 -->
                        <div style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">编制目的</h4>
                            <p style="color: #ecf0f1; font-size: 16px; line-height: 1.6; margin: 0;">
                                为规范和加强公路交通突发事件的应急管理工作，指导、协调全区交通运输部门和单位建立和完善应急预案体系，有效应对公路交通突发事件，及时保障、恢复公路交通正常运行，制定本预案。
                            </p>
                        </div>

                        <!-- 事件分级 -->
                        <div style="background: #34495e; padding: 20px; border-radius: 8px;">
                            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">事件分级与响应条件</h4>
                            <div style="display: flex; flex-direction: column; gap: 15px;">
                                <div style="border: 2px solid #e74c3c; border-radius: 8px; padding: 15px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <span style="background: #e74c3c; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅰ</span>
                                        <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">特别重大</span>
                                    </div>
                                    <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;">
                                        造成高速公路、普通国道交通中断，出现大量车辆积压，并影响到周边省域高速公路、普通国道正常运行，且抢修、处置时间预计在48小时以上的。
                                    </p>
                                </div>
                                <div style="border: 2px solid #f39c12; border-radius: 8px; padding: 15px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <span style="background: #f39c12; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅱ</span>
                                        <span style="color: #f39c12; font-weight: bold; font-size: 16px;">重大</span>
                                    </div>
                                    <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;">
                                        造成国道、省道交通中断，出现大量车辆积压，且抢修、处置时间预计在24小时以上的。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="margin-top: 20px; display: flex; justify-content: flex-end; gap: 10px;">
                            <button onclick="downloadPlan()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                                <i class="fas fa-download" style="margin-right: 5px;"></i>下载预案
                            </button>
                            <button onclick="printPlan()" style="background: #27ae60; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                                <i class="fas fa-print" style="margin-right: 5px;"></i>打印预案
                            </button>
                            <button onclick="viewOtherPlans()" style="background: #95a5a6; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                                <i class="fas fa-list" style="margin-right: 5px;"></i>查看其他预案
                            </button>
                        </div>
                    </div>
                    <div class="plan-sub-tab-content" id="organization" style="display: none;">
                        <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">组织体系</h3>
                        <p style="color: #95a5a6; margin-bottom: 20px; font-size: 16px;">自治区应急组织体系由自治区级、市级和县级三级组成。以下为自治区级应急指挥机构详情：</p>

                        <div class="org-structure" style="display: flex; flex-direction: column; gap: 20px;">
                            <!-- 自治区级指挥机构 -->
                            <h4 style="color: #95a5a6; font-size: 18px; border-bottom: 1px solid #95a5a6; padding-bottom: 8px; margin-bottom: 15px;">自治区级应急指挥机构</h4>

                            <!-- 领导小组 -->
                            <div class="org-card" style="border: 2px solid #3498db; border-radius: 8px; overflow: hidden; margin-bottom: 20px;">
                                <div class="card-header" style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-bottom: 1px solid #3498db; display: flex; align-items: center; justify-content: space-between;">
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-sitemap" style="color: #3498db; margin-right: 10px; font-size: 18px;"></i>
                                        <h4 style="color: #3498db; margin: 0; font-size: 18px;">领导小组</h4>
                                        <span style="margin-left: 10px; padding: 2px 8px; border-radius: 12px; font-size: 12px; background: #27ae60; color: white;">层级2</span>
                                    </div>
                                    <div style="display: flex; gap: 5px;">
                                        <span style="padding: 2px 6px; border-radius: 4px; font-size: 12px; background: #e74c3c; color: white;">Ⅰ级</span>
                                        <span style="padding: 2px 6px; border-radius: 4px; font-size: 12px; background: #f39c12; color: white;">Ⅱ级</span>
                                    </div>
                                </div>
                                <div class="card-content" style="padding: 20px;">
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                                        <div>
                                            <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">主要职责</p>
                                            <div style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
                                                （1）在一级响应启动或由上级应急指挥机构统一指挥时，按照上级应急指挥机构指令开展应急处置行动；<br>
                                                （2）统一领导二级低温雨雪冰冻灾害的应急处置工作，发布指挥调度命令，并督导检查执行情况；<br>
                                                （3）组织协调公路、水路、铁路、民航等行业对受灾区域交通状况加密监测，协调抗灾救灾人员、装备、物资运输；<br>
                                                （4）决定启动、终止低温雨雪冰冻灾害二级预警和应急响应；<br>
                                                （5）根据需要，会同自治区有关部门，制定应对低温雨雪冰冻灾害的联合行动方案，并监督实施；<br>
                                                （6）其他相关重大事项。
                                            </div>
                                        </div>
                                        <div>
                                            <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">关联人员/单位</p>
                                            <div style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                                                <ul style="margin: 0; padding: 0; list-style: none;">
                                                    <li style="margin-bottom: 15px;">
                                                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                            <i class="fas fa-university" style="color: #95a5a6; margin-right: 8px;"></i>
                                                            <span style="font-weight: bold; color: #ecf0f1;">自治区交通运输厅</span>
                                                        </div>
                                                        <ul style="margin-left: 20px; padding-left: 15px; border-left: 2px solid #95a5a6; list-style: none;">
                                                            <li style="margin-bottom: 10px;">
                                                                <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                                    <i class="fas fa-users" style="color: #95a5a6; margin-right: 8px;"></i>
                                                                    <span style="font-weight: 500; color: #95a5a6;">厅领导</span>
                                                                </div>
                                                                <ul style="margin-left: 20px; padding-left: 15px; border-left: 1px solid #7f8c8d; list-style: none;">
                                                                    <li style="margin-bottom: 5px;">
                                                                        <div style="color: #ecf0f1;"><span style="color: #95a5a6;">职务:</span> 组长 (厅主要领导)</div>
                                                                        <ul style="margin-left: 20px; font-size: 12px; color: #bdc3c7; list-style: none;">
                                                                            <li><i class="fas fa-user-circle" style="margin-right: 5px;"></i>张三 (<i class="fas fa-phone-alt" style="margin-right: 5px;"></i>13800001111)</li>
                                                                        </ul>
                                                                    </li>
                                                                    <li style="margin-bottom: 5px;">
                                                                        <div style="color: #ecf0f1;"><span style="color: #95a5a6;">职务:</span> 副组长 (分管厅领导)</div>
                                                                        <ul style="margin-left: 20px; font-size: 12px; color: #bdc3c7; list-style: none;">
                                                                            <li><i class="fas fa-user-circle" style="margin-right: 5px;"></i>李四 (<i class="fas fa-phone-alt" style="margin-right: 5px;"></i>13900002222)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 应急工作组 - 公路抢险组 -->
                            <div class="org-card" style="border: 2px solid #f39c12; border-radius: 8px; overflow: hidden; margin-bottom: 20px;">
                                <div class="card-header" style="background: rgba(243, 156, 18, 0.1); padding: 15px; border-bottom: 1px solid #f39c12; display: flex; align-items: center; justify-content: space-between;">
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-hard-hat" style="color: #f39c12; margin-right: 10px; font-size: 18px;"></i>
                                        <h4 style="color: #f39c12; margin: 0; font-size: 18px;">应急工作组 - 公路抢险组</h4>
                                    </div>
                                </div>
                                <div class="card-content" style="padding: 20px;">
                                    <div style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                                        <ul style="margin: 0; padding: 0; list-style: none;">
                                            <li style="margin-bottom: 15px;">
                                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                    <i class="fas fa-university" style="color: #95a5a6; margin-right: 8px;"></i>
                                                    <span style="font-weight: bold; color: #ecf0f1;">自治区公路发展中心</span>
                                                </div>
                                                <ul style="margin-left: 20px; padding-left: 15px; border-left: 2px solid #95a5a6; list-style: none;">
                                                    <li style="margin-bottom: 10px;">
                                                        <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                            <i class="fas fa-cogs" style="color: #95a5a6; margin-right: 8px;"></i>
                                                            <span style="font-weight: 500; color: #95a5a6;">养护保通科</span>
                                                        </div>
                                                        <ul style="margin-left: 20px; padding-left: 15px; border-left: 1px solid #7f8c8d; list-style: none;">
                                                            <li style="margin-bottom: 5px;">
                                                                <div style="color: #ecf0f1;"><span style="color: #95a5a6;">职务:</span> 科长</div>
                                                                <ul style="margin-left: 20px; font-size: 12px; color: #bdc3c7; list-style: none;">
                                                                    <li><i class="fas fa-user-circle" style="margin-right: 5px;"></i>李四 (<i class="fas fa-phone-alt" style="margin-right: 5px;"></i>13900139000)</li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- 应急工作组 - 道路运输保障组 -->
                            <div class="org-card" style="border: 2px solid #27ae60; border-radius: 8px; overflow: hidden; margin-bottom: 20px;">
                                <div class="card-header" style="background: rgba(39, 174, 96, 0.1); padding: 15px; border-bottom: 1px solid #27ae60; display: flex; align-items: center; justify-content: space-between;">
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-bus-alt" style="color: #27ae60; margin-right: 10px; font-size: 18px;"></i>
                                        <h4 style="color: #27ae60; margin: 0; font-size: 18px;">应急工作组 - 道路运输保障组</h4>
                                    </div>
                                </div>
                                <div class="card-content" style="padding: 20px;">
                                    <div style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                                        <ul style="margin: 0; padding: 0; list-style: none;">
                                            <li style="margin-bottom: 15px;">
                                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                    <i class="fas fa-university" style="color: #95a5a6; margin-right: 8px;"></i>
                                                    <span style="font-weight: bold; color: #ecf0f1;">自治区道路运输发展中心</span>
                                                </div>
                                                <ul style="margin-left: 20px; padding-left: 15px; border-left: 2px solid #95a5a6; list-style: none;">
                                                    <li style="margin-bottom: 10px;">
                                                        <div style="display: flex; align-items: center; margin-bottom: 5px;">
                                                            <i class="fas fa-truck" style="color: #95a5a6; margin-right: 8px;"></i>
                                                            <span style="font-weight: 500; color: #95a5a6;">应急运输科</span>
                                                        </div>
                                                        <ul style="margin-left: 20px; padding-left: 15px; border-left: 1px solid #7f8c8d; list-style: none;">
                                                            <li style="margin-bottom: 5px;">
                                                                <div style="color: #ecf0f1;"><span style="color: #95a5a6;">职务:</span> 负责人</div>
                                                                <ul style="margin-left: 20px; font-size: 12px; color: #bdc3c7; list-style: none;">
                                                                    <li><i class="fas fa-user-circle" style="margin-right: 5px;"></i>赵六 (<i class="fas fa-phone-alt" style="margin-right: 5px;"></i>13600136000)</li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="plan-sub-tab-content" id="prevention" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">预防与预警：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                                    </p>
                                </div>
                    <div class="plan-sub-tab-content" id="response" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">应急响应：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                    <div class="plan-sub-tab-content" id="post-processing" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">后期处置：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                    <div class="plan-sub-tab-content" id="emergency-support" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">应急保障：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                    <div class="plan-sub-tab-content" id="plan-management" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">预案管理：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                </div>
            </div>
        </div>
                            </div>
                        </div>

                        <!-- 事件分级 -->
                        <div style="background: #34495e; padding: 20px; border-radius: 8px;">
                            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">事件分级与响应条件</h4>
                            <div style="display: flex; flex-direction: column; gap: 15px;">
                                <div style="border: 2px solid #e74c3c; border-radius: 8px; padding: 15px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <span style="background: #e74c3c; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅰ</span>
                                        <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">特别重大</span>
                                    </div>
                                    <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;">
                                        造成高速公路、普通国道交通中断，出现大量车辆积压，并影响到周边省域高速公路、普通国道正常运行，且抢修、处置时间预计在48小时以上的。
                                    </p>
                                </div>
                                <div style="border: 2px solid #f39c12; border-radius: 8px; padding: 15px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <span style="background: #f39c12; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅱ</span>
                                        <span style="color: #f39c12; font-weight: bold; font-size: 16px;">重大</span>
                                    </div>
                                    <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;">
                                        造成国道、省道交通中断，出现大量车辆积压，且抢修、处置时间预计在24小时以上的。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div style="margin-top: 20px; display: flex; justify-content: flex-end; gap: 10px;">
                            <button onclick="downloadPlan()" style="background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                                <i class="fas fa-download" style="margin-right: 5px;"></i>下载预案
                            </button>
                            <button onclick="printPlan()" style="background: #27ae60; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                                <i class="fas fa-print" style="margin-right: 5px;"></i>打印预案
                            </button>
                            <button onclick="viewOtherPlans()" style="background: #95a5a6; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
                                <i class="fas fa-list" style="margin-right: 5px;"></i>查看其他预案
                            </button>
                        </div>
                    </div>

                    <div class="plan-sub-tab-content" id="prevention" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">预防与预警：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                                    </p>
                                </div>
                    <div class="plan-sub-tab-content" id="response" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">应急响应：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                    <div class="plan-sub-tab-content" id="post-processing" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">后期处置：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                    <div class="plan-sub-tab-content" id="emergency-support" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">应急保障：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                    <div class="plan-sub-tab-content" id="plan-management" style="display: none;">
                        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">预案管理：</h4>
                        <p style="font-size: 18px; margin-bottom: 10px;">
                            1. 应急救援队伍组织：
                            <br>
                            - 柳州市消防救援支队
                            <br>
                            - 柳州市公安消防救援队
                            <br>
                            - 柳州市交通运输局应急救援队
                            <br>
                            - 柳州市水利局应急救援队
                            <br>
                            - 柳州市环境保护局应急救援队
                            <br>
                            - 柳州市安全生产监督管理局应急救援队
                            <br>
                            - 柳州市工商行政管理局应急救援队
                            <br>
                            - 柳州市质量技术监督局应急救援队
                            <br>
                            - 柳州市文化和旅游局应急救援队
                            <br>
                            - 柳州市教育局应急救援队
                            <br>
                            - 柳州市卫生健康局应急救援队
                            <br>
                            - 柳州市住房和城乡建设局应急救援队
                            <br>
                            - 柳州市人民政府应急救援队
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 其他预案模态框 -->
    <div id="other-plans-modal" class="modal" style="display: none; z-index: 10000 !important;">
        <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">其他预案</h3>
                <span class="close" onclick="closeOtherPlansModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">
                <div class="other-plans-content">
                    <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">其他预案列表：</h4>
                    <div class="plans-search-bar" style="margin-bottom: 20px;">
                        <input type="text" id="plans-search" placeholder="搜索预案..." style="width: 100%; padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
                    </div>
                    <div class="plans-filter" style="margin-bottom: 20px;">
                        <select id="plans-category" style="padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
                            <option value="">全部类别</option>
                            <option value="traffic">交通运输</option>
                            <option value="safety">安全生产</option>
                            <option value="natural">自然灾害</option>
                            <option value="public">公共卫生</option>
                        </select>
                        <select id="plans-level" style="padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
                            <option value="">全部级别</option>
                            <option value="1">Ⅰ级</option>
                            <option value="2">Ⅱ级</option>
                            <option value="3">Ⅲ级</option>
                            <option value="4">Ⅳ级</option>
                        </select>
                        <button onclick="searchPlans()" style="background: #3498db; color: white; padding: 12px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">搜索</button>
                    </div>
                    <div class="plans-list">
                        <div class="plan-item" style="background: #34495e; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <div class="plan-info" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">预案名称：</span>
                                    <span>柳州市交通运输应急抢险救援预案</span>
                                </div>
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">类别：</span>
                                    <span>交通运输</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: flex-end;">
                                <button onclick="viewPlanDetails('plan001')" style="background: #3498db; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-right: 10px;">查看详情</button>
                                <button onclick="downloadPlan()" style="background: #27ae60; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">下载</button>
                            </div>
                        </div>
                        <div class="plan-item" style="background: #34495e; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <div class="plan-info" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">预案名称：</span>
                                    <span>柳州市安全生产应急抢险救援预案</span>
                                </div>
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">类别：</span>
                                    <span>安全生产</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: flex-end;">
                                <button onclick="viewPlanDetails('plan002')" style="background: #3498db; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-right: 10px;">查看详情</button>
                                <button onclick="downloadPlan()" style="background: #27ae60; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">下载</button>
                            </div>
                        </div>
                        <div class="plan-item" style="background: #34495e; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <div class="plan-info" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">预案名称：</span>
                                    <span>柳州市自然灾害应急抢险救援预案</span>
                                </div>
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">类别：</span>
                                    <span>自然灾害</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: flex-end;">
                                <button onclick="viewPlanDetails('plan003')" style="background: #3498db; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-right: 10px;">查看详情</button>
                                <button onclick="downloadPlan()" style="background: #27ae60; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">下载</button>
                            </div>
                        </div>
                        <div class="plan-item" style="background: #34495e; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <div class="plan-info" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">预案名称：</span>
                                    <span>柳州市公共卫生应急抢险救援预案</span>
                                </div>
                                <div style="font-size: 18px;">
                                    <span style="color: #3498db;">类别：</span>
                                    <span>公共卫生</span>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: flex-end;">
                                <button onclick="viewPlanDetails('plan004')" style="background: #3498db; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-right: 10px;">查看详情</button>
                                <button onclick="downloadPlan()" style="background: #27ae60; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">下载</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共JavaScript -->
    <script src="js/emergency-common.js"></script>

    <!-- 应急一张图专用JavaScript -->
    <script>
        // 全局变量，用于跟踪当前显示的标点类型
        let currentMarkerType = 'events'; // 'events', 'supplies', 'teams', 'others'

        // 全选/全不选复选框的处理函数
        function toggleAllMarkers() {
            const checkbox = document.getElementById('res-type-all');
            const isChecked = checkbox.checked;
            console.log('全选/全不选复选框状态:', isChecked);

            // 显示/隐藏所有类型的标点
            if (isChecked) {
                // 全选时显示所有标点
                document.querySelectorAll('.event-marker, .supply-marker, .team-marker, .vehicle-marker, .medical-marker, .fire-marker').forEach(marker => {
                    marker.style.display = 'block';
                });
            } else {
                // 全不选时隐藏所有标点
                document.querySelectorAll('.event-marker, .supply-marker, .team-marker, .vehicle-marker, .medical-marker, .fire-marker').forEach(marker => {
                    marker.style.display = 'none';
                });
            }
        }

        // 显示应急事件
        function showEmergencyEvents() {
            console.log('显示应急事件');
            currentMarkerType = 'events';

            // 隐藏所有其他类型标点
            document.querySelectorAll('.supply-marker, .team-marker, .vehicle-marker, .medical-marker, .fire-marker').forEach(marker => {
                marker.style.display = 'none';
            });

            // 显示应急事件标点
            document.querySelectorAll('.event-marker').forEach(marker => {
                marker.style.display = 'block';
            });

            // 更新标签卡状态
            updateTabButtons(0);

            // 显示对应的筛选内容
            showTabContent('emergency-events-content');
        }

        // 显示应急物资
        function showEmergencySupplies() {
            console.log('显示应急物资');
            currentMarkerType = 'supplies';

            // 隐藏所有其他类型标点
            document.querySelectorAll('.event-marker, .team-marker, .vehicle-marker, .medical-marker, .fire-marker').forEach(marker => {
                marker.style.display = 'none';
            });

            // 显示应急物资标点
            document.querySelectorAll('.supply-marker').forEach(marker => {
                marker.style.display = 'block';
            });

            // 更新标签卡状态
            updateTabButtons(1);

            // 显示对应的筛选内容
            showTabContent('emergency-supplies-content');
        }

        // 显示救援队伍
        function showRescueTeams() {
            console.log('显示救援队伍');
            currentMarkerType = 'teams';

            // 隐藏所有其他类型标点
            document.querySelectorAll('.event-marker, .supply-marker, .vehicle-marker, .medical-marker, .fire-marker').forEach(marker => {
                marker.style.display = 'none';
            });

            // 显示救援队伍标点
            document.querySelectorAll('.team-marker').forEach(marker => {
                marker.style.display = 'block';
            });

            // 更新标签卡状态
            updateTabButtons(2);

            // 显示对应的筛选内容
            showTabContent('rescue-teams-content');
        }

        // 显示其他资源
        function showOtherResources() {
            console.log('显示其他资源');
            currentMarkerType = 'others';

            // 隐藏所有其他类型标点
            document.querySelectorAll('.event-marker, .supply-marker, .team-marker').forEach(marker => {
                marker.style.display = 'none';
            });

            // 显示其他资源标点
            document.querySelectorAll('.vehicle-marker, .medical-marker, .fire-marker').forEach(marker => {
                marker.style.display = 'block';
            });

            // 更新标签卡状态
            updateTabButtons(3);

            // 显示对应的筛选内容
            showTabContent('other-resources-content');
        }

        // 更新标签卡按钮状态
        function updateTabButtons(activeIndex) {
            const buttons = document.querySelectorAll('.resource-tab-button');
            buttons.forEach((btn, index) => {
                if (index === activeIndex) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }

        // 显示对应的筛选内容
        function showTabContent(activeContentId) {
            const contents = document.querySelectorAll('.resource-tab-content');
            contents.forEach(content => {
                if (content.id === activeContentId) {
                    content.style.display = 'block';
                    content.classList.add('active');
                } else {
                    content.style.display = 'none';
                    content.classList.remove('active');
                }
            });
        }

        // 筛选标签切换函数
        function switchFilterTab(button, tabType) {
            // 移除所有按钮的active类
            const parentContainer = button.closest('.filter-tabs');
            if (parentContainer) {
                const siblingButtons = parentContainer.querySelectorAll('.filter-tab-button');
                siblingButtons.forEach(btn => btn.classList.remove('active'));
            }

            // 添加当前按钮的active类
            button.classList.add('active');

            // 获取目标内容ID
            const targetTabContentId = `filter-by-${tabType}-content`;

            // 获取相关的内容元素
            const parentSection = button.closest('.resource-condition-filter');
            if (parentSection) {
                const contentElements = parentSection.querySelectorAll('.filter-tab-content');

                contentElements.forEach(content => {
                    if (content.id === targetTabContentId) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            }
        }

        // 应急事件标签切换函数
        function switchAlertTab(button, tabType) {
            console.log('切换应急事件标签:', tabType);

            // 移除所有按钮的active类并重置样式
            const parentContainer = button.closest('.alert-tabs');
            if (parentContainer) {
                const siblingButtons = parentContainer.querySelectorAll('.alert-tab-button');
                siblingButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.style.borderBottom = '2px solid transparent';
                    btn.style.color = '#666';
                    btn.style.fontWeight = 'normal';
                });
            }

            // 添加当前按钮的active类并设置样式
            button.classList.add('active');
            button.style.borderBottom = '2px solid #ff6b35';
            button.style.color = '#333';
            button.style.fontWeight = 'bold';

            // 获取目标内容ID
            const targetTabContentId = `alert-${tabType}-content`;

            // 获取相关的内容元素
            const parentSection = button.closest('.alert-list-container');
            if (parentSection) {
                const contentElements = parentSection.querySelectorAll('.alert-tab-content');

                contentElements.forEach(content => {
                    if (content.id === targetTabContentId) {
                        content.style.display = 'block';
                        content.classList.add('active');
                        console.log('显示内容:', content.id);
                    } else {
                        content.style.display = 'none';
                        content.classList.remove('active');
                        console.log('隐藏内容:', content.id);
                    }
                });
            }
        }

        // 显示应急事件详情
        function showEventDetails() {
            console.log('显示应急事件详情');
            updateDetailsTabButtons(0);
            showDetailsContent('details-events-content');
        }

        // 显示救援队伍详情
        function showTeamDetails() {
            console.log('显示救援队伍详情');
            updateDetailsTabButtons(1);
            showDetailsContent('details-teams-content');
        }

        // 显示物资储备详情
        function showSupplyDetails() {
            console.log('显示物资储备详情');
            updateDetailsTabButtons(2);
            showDetailsContent('details-supplies-content');
        }

        // 显示专家库详情
        function showExpertDetails() {
            console.log('显示专家库详情');
            updateDetailsTabButtons(3);
            showDetailsContent('details-experts-content');
        }

        // 更新详情标签卡按钮状态
        function updateDetailsTabButtons(activeIndex) {
            const buttons = document.querySelectorAll('.details-tab-button');
            buttons.forEach((btn, index) => {
                if (index === activeIndex) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }

        // 显示对应的详情内容
        function showDetailsContent(activeContentId) {
            const contents = document.querySelectorAll('.details-tab-content');
            contents.forEach(content => {
                if (content.id === activeContentId) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        }

        // 应急事件模态框相关函数
        function openEmergencyEventModal() {
            console.log('打开应急事件模态框');
            const modal = document.getElementById('emergency-event-modal');
            console.log('找到的模态框元素:', modal);
            if (modal) {
                console.log('设置模态框显示');
                modal.style.display = 'block';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                modal.style.zIndex = '9999';
                document.body.style.overflow = 'hidden'; // 防止背景滚动
                console.log('模态框应该已经显示');
            } else {
                console.error('未找到ID为emergency-event-modal的元素');
            }
        }

        function closeEmergencyEventModal() {
            console.log('关闭应急事件模态框');
            const modal = document.getElementById('emergency-event-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // 导出事件信息
        function exportEventInfo() {
            console.log('导出事件信息');
            alert('导出功能开发中...');
        }

        // 编辑辅助决策
        function editDecision() {
            console.log('编辑辅助决策');
            const displayDiv = document.querySelector('.decision-display');
            const editSection = document.querySelector('.decision-edit-section');
            const editText = document.getElementById('decision-edit-text');

            if (displayDiv && editSection && editText) {
                // 将当前显示的内容复制到编辑框
                editText.value = displayDiv.textContent.trim();

                // 隐藏显示区域，显示编辑区域
                displayDiv.style.display = 'none';
                editSection.style.display = 'block';
            }
        }

        // 确认决策编辑
        function confirmDecisionEdit() {
            console.log('确认决策编辑');
            const displayDiv = document.querySelector('.decision-display');
            const editSection = document.querySelector('.decision-edit-section');
            const editText = document.getElementById('decision-edit-text');

            if (displayDiv && editSection && editText) {
                // 更新显示内容
                displayDiv.textContent = editText.value;

                // 显示显示区域，隐藏编辑区域
                displayDiv.style.display = 'block';
                editSection.style.display = 'none';
            }
        }

        // 取消决策编辑
        function cancelDecisionEdit() {
            console.log('取消决策编辑');
            const displayDiv = document.querySelector('.decision-display');
            const editSection = document.querySelector('.decision-edit-section');

            if (displayDiv && editSection) {
                // 显示显示区域，隐藏编辑区域
                displayDiv.style.display = 'block';
                editSection.style.display = 'none';
            }
        }

        // 预案相关函数
        // 查看预案详情
        function viewPlanDetails(planId) {
            console.log('查看预案详情:', planId);
            const modal = document.getElementById('plan-details-modal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // 初始化子标签页
                initPlanSubTabs();
            }
        }

        // 初始化预案子标签页
        function initPlanSubTabs() {
            // 为所有子标签按钮添加点击事件
            const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
            subTabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-plan-tab');
                    switchPlanSubTab(targetTab);
                });
            });

            // 默认显示第一个标签页
            switchPlanSubTab('basic-info');
        }

        // 切换预案子标签页
        function switchPlanSubTab(targetTab) {
            // 更新按钮状态
            const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
            subTabBtns.forEach(btn => {
                if (btn.getAttribute('data-plan-tab') === targetTab) {
                    btn.classList.add('active');
                    btn.style.color = '#3498db';
                    btn.style.borderBottomColor = '#3498db';
                } else {
                    btn.classList.remove('active');
                    btn.style.color = '#95a5a6';
                    btn.style.borderBottomColor = 'transparent';
                }
            });

            // 更新内容显示
            const subTabContents = document.querySelectorAll('.plan-sub-tab-content');
            subTabContents.forEach(content => {
                if (content.id === targetTab) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        }

        // 关闭预案详情模态框
        function closePlanDetailsModal() {
            console.log('关闭预案详情模态框');
            const modal = document.getElementById('plan-details-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 查看其他预案
        function viewOtherPlans() {
            console.log('查看其他预案');
            // 先关闭预案详情模态框
            closePlanDetailsModal();
            
            const modal = document.getElementById('other-plans-modal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭其他预案模态框
        function closeOtherPlansModal() {
            console.log('关闭其他预案模态框');
            const modal = document.getElementById('other-plans-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 搜索预案
        function searchPlans() {
            console.log('搜索预案');
            const searchTerm = document.getElementById('plans-search').value.toLowerCase();
            const category = document.getElementById('plans-category').value;
            const level = document.getElementById('plans-level').value;
            
            const planItems = document.querySelectorAll('.plan-item');
            
            planItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                const planCategory = item.querySelector('.plan-info').textContent;
                const planLevel = item.querySelector('.plan-info').textContent;
                
                let showItem = true;
                
                // 搜索关键词过滤
                if (searchTerm && !text.includes(searchTerm)) {
                    showItem = false;
                }
                
                // 类别过滤
                if (category && !planCategory.includes(category === 'traffic' ? '交通运输' : 
                                                      category === 'safety' ? '安全生产' : 
                                                      category === 'natural' ? '自然灾害' : 
                                                      category === 'public' ? '公共卫生' : '')) {
                    showItem = false;
                }
                
                // 级别过滤
                if (level && !planLevel.includes(level === '1' ? 'Ⅰ级' : 
                                                 level === '2' ? 'Ⅱ级' : 
                                                 level === '3' ? 'Ⅲ级' : 
                                                 level === '4' ? 'Ⅳ级' : '')) {
                    showItem = false;
                }
                
                item.style.display = showItem ? 'block' : 'none';
            });
        }

        // 下载预案
        function downloadPlan() {
            console.log('下载预案');
            alert('预案下载功能开发中...');
        }

        // 打印预案
        function printPlan() {
            console.log('打印预案');
            window.print();
        }

        function createMapMarker(item, container, color, size) {
            const marker = document.createElement('div');
            marker.className = 'circle-resource-marker';
            marker.setAttribute('data-resource-id', item.id);
            marker.style.cssText = `
                position: absolute;
                top: ${item.y}%;
                left: ${item.x}%;
                width: ${size}px;
                height: ${size}px;
                background: ${color};
                border: 2px solid #fff;
                border-radius: 50%;
                cursor: pointer;
                z-index: 1002;
                box-shadow: 0 2px 8px ${color}66;
                transition: all 0.3s ease;
            `;

            // 创建信息卡片
            const tooltip = document.createElement('div');
            tooltip.className = 'circle-marker-tooltip';
            tooltip.innerHTML = `
                <div class="tooltip-title">${item.name}</div>
                <div class="tooltip-info">
                    <div class="tooltip-item">
                        <span class="tooltip-label">距离:</span>
                        <span class="tooltip-value">${item.distance}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">地点:</span>
                        <span class="tooltip-value">${item.location}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">负责人:</span>
                        <span class="tooltip-value">${item.manager}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">联系方式:</span>
                        <span class="tooltip-value">${item.contact}</span>
                    </div>
                </div>
            `;

            // 添加悬停效果和信息卡片显示
            marker.addEventListener('mouseenter', function(e) {
                this.style.transform = 'scale(1.5)';
                this.style.zIndex = '1003';

                // 显示信息卡片
                const rect = this.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();

                tooltip.style.left = (rect.left - containerRect.left + 20) + 'px';
                tooltip.style.top = (rect.top - containerRect.top - 10) + 'px';

                container.appendChild(tooltip);
                setTimeout(() => tooltip.classList.add('show'), 10);
            });

            marker.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.zIndex = '1002';

                // 隐藏信息卡片
                tooltip.classList.remove('show');
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 300);
            });

            container.appendChild(marker);
        }

        // 生成资源列表
        function generateResourceList(data, resourceType) {
            const listContainer = document.getElementById('resource-list-container');
            const panelTitle = document.getElementById('resource-panel-title');

            if (!listContainer || !panelTitle) return;

            // 设置面板标题
            const typeNames = {
                'supplies': '应急物资',
                'teams': '救援队伍',
                'vehicles': '救援车辆',
                'medical': '医疗单位',
                'fire': '消防单位',
                'experts': '应急专家'
            };
            panelTitle.textContent = `${typeNames[resourceType] || '资源'}列表`;

            // 清空列表
            listContainer.innerHTML = '';

            // 合并20km和40km的数据
            const allResources = [...data['20km'], ...data['40km']];

            // 按距离排序
            allResources.sort((a, b) => {
                const distanceA = parseInt(a.distance.replace('km', ''));
                const distanceB = parseInt(b.distance.replace('km', ''));
                return distanceA - distanceB;
            });

            // 生成列表项
            allResources.forEach(item => {
                const listItem = createResourceListItem(item, resourceType);
                listContainer.appendChild(listItem);
            });
        }

        // 创建资源列表项
        function createResourceListItem(item, resourceType) {
            const listItem = document.createElement('div');
            listItem.className = 'resource-list-item';
            listItem.setAttribute('data-resource-id', item.id);

            // 根据距离确定颜色
            const distance = parseInt(item.distance.replace('km', ''));
            const rangeColor = distance <= 20 ? '#3498db' : '#27ae60';
            const rangeText = distance <= 20 ? '20km内' : '40km内';

            listItem.style.cssText = `
                background: #2c3e50;
                border: 1px solid #95a5a6;
                border-radius: 6px;
                padding: 15px;
                cursor: pointer;
                transition: all 0.3s ease;
                border-left: 4px solid ${rangeColor};
            `;

            listItem.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                    <h6 style="color: #3498db; margin: 0; font-size: 20px; font-weight: bold;">${item.name}</h6>
                    <span style="background: ${rangeColor}; color: white; padding: 4px 10px; border-radius: 12px; font-size: 14px; font-weight: bold;">${rangeText}</span>
                </div>
                <div style="display: flex; flex-direction: column; gap: 6px; font-size: 16px;">
                    <div style="color: #95a5a6;">
                        <span style="color: #ecf0f1;">${item.location}</span>
                    </div>
                    <div style="color: #95a5a6;">
                        <span style="color: #ecf0f1;">距离 ${item.distance}</span>
                    </div>
                    <div style="color: #95a5a6;">
                        <span style="color: #ecf0f1;">${item.manager}</span>
                    </div>
                    <div style="color: #95a5a6;">
                        <span style="color: #ecf0f1;">${item.contact}</span>
                    </div>
                </div>
            `;

            // 添加悬停效果
            listItem.addEventListener('mouseenter', function() {
                this.style.background = '#34495e';
                this.style.borderColor = '#3498db';
                this.style.transform = 'translateX(5px)';

                // 高亮对应的地图标点
                highlightMapMarker(item.id, true);
            });

            listItem.addEventListener('mouseleave', function() {
                this.style.background = '#2c3e50';
                this.style.borderColor = '#95a5a6';
                this.style.transform = 'translateX(0)';

                // 取消高亮地图标点
                highlightMapMarker(item.id, false);
            });

            // 添加点击事件
            listItem.addEventListener('click', function() {
                // 聚焦到对应的地图标点
                focusOnMapMarker(item.id);
            });

            return listItem;
        }

        // 高亮地图标点
        function highlightMapMarker(resourceId, highlight) {
            const marker = document.querySelector(`[data-resource-id="${resourceId}"]`);
            if (marker) {
                if (highlight) {
                    marker.style.transform = 'scale(1.8)';
                    marker.style.zIndex = '1004';
                    marker.style.boxShadow = '0 0 20px #f39c12, 0 0 40px #f39c12';
                } else {
                    marker.style.transform = 'scale(1)';
                    marker.style.zIndex = '1002';
                    marker.style.boxShadow = marker.style.background.includes('#3498db') ?
                        '0 2px 8px #3498db66' : '0 2px 8px #27ae6066';
                }
            }
        }

        // 聚焦到地图标点
        function focusOnMapMarker(resourceId) {
            const marker = document.querySelector(`[data-resource-id="${resourceId}"]`);
            if (marker) {
                // 添加聚焦动画
                marker.style.animation = 'pulse 1s ease-in-out 3';
                marker.style.transform = 'scale(2)';
                marker.style.zIndex = '1005';
                marker.style.boxShadow = '0 0 30px #e74c3c, 0 0 60px #e74c3c';

                // 3秒后恢复正常
                setTimeout(() => {
                    marker.style.animation = '';
                    marker.style.transform = 'scale(1)';
                    marker.style.zIndex = '1002';
                    marker.style.boxShadow = marker.style.background.includes('#3498db') ?
                        '0 2px 8px #3498db66' : '0 2px 8px #27ae6066';
                }, 3000);
            }
        }

        // 初始化搜索功能
        function initResourceSearch() {
            const searchInput = document.getElementById('resource-search-input');
            if (!searchInput) return;

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                filterResourceList(searchTerm);
            });
        }

        // 过滤资源列表
        function filterResourceList(searchTerm) {
            const listItems = document.querySelectorAll('.resource-list-item');

            listItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (searchTerm === '' || text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 点击模态框外部关闭模态框
        window.onclick = function(event) {
            const emergencyModal = document.getElementById('emergency-event-modal');
            const supplyModal = document.getElementById('emergency-supply-modal');
            const teamModal = document.getElementById('rescue-team-modal');
            const fireModal = document.getElementById('fire-station-modal');
            const medicalModal = document.getElementById('medical-station-modal');
            const vehicleModal = document.getElementById('rescue-vehicle-modal');
            const circleModal = document.getElementById('emergency-circle-modal');
            const planDetailsModal = document.getElementById('plan-details-modal');
            const otherPlansModal = document.getElementById('other-plans-modal');

            if (event.target === emergencyModal) {
                closeEmergencyEventModal();
            } else if (event.target === supplyModal) {
                closeSupplyModal();
            } else if (event.target === teamModal) {
                closeTeamModal();
            } else if (event.target === fireModal) {
                closeFireStationModal();
            } else if (event.target === medicalModal) {
                closeMedicalStationModal();
            } else if (event.target === vehicleModal) {
                closeRescueVehicleModal();
            } else if (event.target === circleModal) {
                closeCircleModal();
            } else if (event.target === planDetailsModal) {
                closePlanDetailsModal();
            } else if (event.target === otherPlansModal) {
                closeOtherPlansModal();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('应急一张图页面已加载');

            // 默认显示应急事件
            showEmergencyEvents();

            // 为所有标注点添加点击事件
            document.querySelectorAll('.map-marker').forEach(marker => {
                marker.addEventListener('click', function() {
                    const markerId = this.getAttribute('data-id');
                    const markerType = this.getAttribute('data-type');
                    console.log(`点击了${markerType}标点: ${markerId}`);

                    // 根据标点类型打开相应的模态框
                    if (markerType === 'emergency-event' || markerType === 'events') {
                        console.log('触发应急事件模态框，标点类型:', markerType, '标点ID:', markerId);
                        openEmergencyEventModal();
                    } else if (markerType === 'supplies' || markerType === 'supply') {
                        console.log('触发应急物资模态框，标点类型:', markerType, '标点ID:', markerId);
                        openSupplyModal();
                    } else if (markerType === 'teams' || markerType === 'team') {
                        console.log('触发救援队伍模态框，标点类型:', markerType, '标点ID:', markerId);
                        openTeamModal();
                    } else if (markerType === 'fire' || markerType === 'fire-station') {
                        console.log('触发消防点模态框，标点类型:', markerType, '标点ID:', markerId);
                        openFireStationModal();
                    } else if (markerType === 'medical' || markerType === 'medical-station') {
                        console.log('触发医疗点模态框，标点类型:', markerType, '标点ID:', markerId);
                        openMedicalStationModal();
                    } else if (markerType === 'vehicle' || markerType === 'rescue-vehicle' || markerType === 'vehicles') {
                        console.log('触发救援车辆模态框，标点类型:', markerType, '标点ID:', markerId);
                        openRescueVehicleModal();
                    }
                });

                // 添加鼠标悬停效果
                marker.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2)';
                    this.style.zIndex = '1001';
                });

                marker.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.zIndex = '1000';
                });
            });
        });

        // 初始化导航栏
        NavigationComponent.init('emergency-map');

        // 预案相关函数
        // 查看预案详情
        function viewPlanDetails(planId) {
            console.log('查看预案详情:', planId);
            const modal = document.getElementById('plan-details-modal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // 初始化子标签页
                initPlanSubTabs();
            }
        }

        // 初始化预案子标签页
        function initPlanSubTabs() {
            // 为所有子标签按钮添加点击事件
            const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
            subTabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-plan-tab');
                    switchPlanSubTab(targetTab);
                });
            });

            // 默认显示第一个标签页
            switchPlanSubTab('basic-info');
        }

        // 切换预案子标签页
        function switchPlanSubTab(targetTab) {
            // 更新按钮状态
            const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
            subTabBtns.forEach(btn => {
                if (btn.getAttribute('data-plan-tab') === targetTab) {
                    btn.classList.add('active');
                    btn.style.color = '#3498db';
                    btn.style.borderBottomColor = '#3498db';
                } else {
                    btn.classList.remove('active');
                    btn.style.color = '#95a5a6';
                    btn.style.borderBottomColor = 'transparent';
                }
            });

            // 更新内容显示
            const subTabContents = document.querySelectorAll('.plan-sub-tab-content');
            subTabContents.forEach(content => {
                if (content.id === targetTab) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        }

        // 关闭预案详情模态框
        function closePlanDetailsModal() {
            console.log('关闭预案详情模态框');
            const modal = document.getElementById('plan-details-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 查看其他预案
        function viewOtherPlans() {
            console.log('查看其他预案');
            // 先关闭预案详情模态框
            closePlanDetailsModal();
            
            const modal = document.getElementById('other-plans-modal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭其他预案模态框
        function closeOtherPlansModal() {
            console.log('关闭其他预案模态框');
            const modal = document.getElementById('other-plans-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 搜索预案
        function searchPlans() {
            console.log('搜索预案');
            const searchTerm = document.getElementById('plans-search').value.toLowerCase();
            const category = document.getElementById('plans-category').value;
            const level = document.getElementById('plans-level').value;
            
            const planItems = document.querySelectorAll('.plan-item');
            
            planItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                const planCategory = item.querySelector('.plan-info').textContent;
                const planLevel = item.querySelector('.plan-info').textContent;
                
                let showItem = true;
                
                // 搜索关键词过滤
                if (searchTerm && !text.includes(searchTerm)) {
                    showItem = false;
                }
                
                // 类别过滤
                if (category && !planCategory.includes(category === 'traffic' ? '交通运输' : 
                                                      category === 'safety' ? '安全生产' : 
                                                      category === 'natural' ? '自然灾害' : 
                                                      category === 'public' ? '公共卫生' : '')) {
                    showItem = false;
                }
                
                // 级别过滤
                if (level && !planLevel.includes(level === '1' ? 'Ⅰ级' : 
                                                 level === '2' ? 'Ⅱ级' : 
                                                 level === '3' ? 'Ⅲ级' : 
                                                 level === '4' ? 'Ⅳ级' : '')) {
                    showItem = false;
                }
                
                item.style.display = showItem ? 'block' : 'none';
            });
        }

        // 下载预案
        function downloadPlan() {
            console.log('下载预案');
            alert('预案下载功能开发中...');
        }

        // 预案相关函数
        // 查看预案详情
        function viewPlanDetails(planId) {
            console.log('查看预案详情:', planId);
            const modal = document.getElementById('plan-details-modal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // 初始化子标签页
                initPlanSubTabs();
            }
        }

        // 初始化预案子标签页
        function initPlanSubTabs() {
            // 为所有子标签按钮添加点击事件
            const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
            subTabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-plan-tab');
                    switchPlanSubTab(targetTab);
                });
            });

            // 默认显示第一个标签页
            switchPlanSubTab('basic-info');
        }

        // 切换预案子标签页
        function switchPlanSubTab(targetTab) {
            // 更新按钮状态
            const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
            subTabBtns.forEach(btn => {
                if (btn.getAttribute('data-plan-tab') === targetTab) {
                    btn.classList.add('active');
                    btn.style.color = '#3498db';
                    btn.style.borderBottomColor = '#3498db';
                } else {
                    btn.classList.remove('active');
                    btn.style.color = '#95a5a6';
                    btn.style.borderBottomColor = 'transparent';
                }
            });

            // 更新内容显示
            const subTabContents = document.querySelectorAll('.plan-sub-tab-content');
            subTabContents.forEach(content => {
                if (content.id === targetTab) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
        }

        // 关闭预案详情模态框
        function closePlanDetailsModal() {
            console.log('关闭预案详情模态框');
            const modal = document.getElementById('plan-details-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }
</script>
</body>
</html>
